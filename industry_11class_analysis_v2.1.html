<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"><head>

<meta charset="utf-8">
<meta name="generator" content="quarto-1.6.39">

<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">

<meta name="author" content="ESG China Research">
<meta name="dcterms.date" content="2025-08-10">

<title>11类行业分类回归分析</title>
<style>
code{white-space: pre-wrap;}
span.smallcaps{font-variant: small-caps;}
div.columns{display: flex; gap: min(4vw, 1.5em);}
div.column{flex: auto; overflow-x: auto;}
div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
ul.task-list{list-style: none;}
ul.task-list li input[type="checkbox"] {
  width: 0.8em;
  margin: 0 0.8em 0.2em -1em; /* quarto-specific, see https://github.com/quarto-dev/quarto-cli/issues/4556 */ 
  vertical-align: middle;
}
/* CSS for syntax highlighting */
pre > code.sourceCode { white-space: pre; position: relative; }
pre > code.sourceCode > span { line-height: 1.25; }
pre > code.sourceCode > span:empty { height: 1.2em; }
.sourceCode { overflow: visible; }
code.sourceCode > span { color: inherit; text-decoration: inherit; }
div.sourceCode { margin: 1em 0; }
pre.sourceCode { margin: 0; }
@media screen {
div.sourceCode { overflow: auto; }
}
@media print {
pre > code.sourceCode { white-space: pre-wrap; }
pre > code.sourceCode > span { display: inline-block; text-indent: -5em; padding-left: 5em; }
}
pre.numberSource code
  { counter-reset: source-line 0; }
pre.numberSource code > span
  { position: relative; left: -4em; counter-increment: source-line; }
pre.numberSource code > span > a:first-child::before
  { content: counter(source-line);
    position: relative; left: -1em; text-align: right; vertical-align: baseline;
    border: none; display: inline-block;
    -webkit-touch-callout: none; -webkit-user-select: none;
    -khtml-user-select: none; -moz-user-select: none;
    -ms-user-select: none; user-select: none;
    padding: 0 4px; width: 4em;
  }
pre.numberSource { margin-left: 3em;  padding-left: 4px; }
div.sourceCode
  {   }
@media screen {
pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
}
</style>


<script src="industry_11class_analysis_v2.1_files/libs/clipboard/clipboard.min.js"></script>
<script src="industry_11class_analysis_v2.1_files/libs/quarto-html/quarto.js"></script>
<script src="industry_11class_analysis_v2.1_files/libs/quarto-html/popper.min.js"></script>
<script src="industry_11class_analysis_v2.1_files/libs/quarto-html/tippy.umd.min.js"></script>
<script src="industry_11class_analysis_v2.1_files/libs/quarto-html/anchor.min.js"></script>
<link href="industry_11class_analysis_v2.1_files/libs/quarto-html/tippy.css" rel="stylesheet">
<link href="industry_11class_analysis_v2.1_files/libs/quarto-html/quarto-syntax-highlighting-e26003cea8cd680ca0c55a263523d882.css" rel="stylesheet" id="quarto-text-highlighting-styles">
<script src="industry_11class_analysis_v2.1_files/libs/bootstrap/bootstrap.min.js"></script>
<link href="industry_11class_analysis_v2.1_files/libs/bootstrap/bootstrap-icons.css" rel="stylesheet">
<link href="industry_11class_analysis_v2.1_files/libs/bootstrap/bootstrap-a1ff8711b79ae3724c050874b28d9907.min.css" rel="stylesheet" append-hash="true" id="quarto-bootstrap" data-mode="light">


</head>

<body>

<div id="quarto-content" class="page-columns page-rows-contents page-layout-article">
<div id="quarto-margin-sidebar" class="sidebar margin-sidebar">
  <nav id="TOC" role="doc-toc" class="toc-active">
    <h2 id="toc-title">Table of contents</h2>
   
  <ul>
  <li><a href="#数据准备" id="toc-数据准备" class="nav-link active" data-scroll-target="#数据准备">数据准备</a>
  <ul class="collapse">
  <li><a href="#加载必要的库和数据" id="toc-加载必要的库和数据" class="nav-link" data-scroll-target="#加载必要的库和数据">加载必要的库和数据</a></li>
  <li><a href="#创建11类行业分类" id="toc-创建11类行业分类" class="nav-link" data-scroll-target="#创建11类行业分类">创建11类行业分类</a></li>
  <li><a href="#na值进一步分类方案" id="toc-na值进一步分类方案" class="nav-link" data-scroll-target="#na值进一步分类方案">NA值进一步分类方案</a></li>
  <li><a href="#创建完整的12类分类包含other类" id="toc-创建完整的12类分类包含other类" class="nav-link" data-scroll-target="#创建完整的12类分类包含other类">创建完整的12类分类（包含Other类）</a></li>
  </ul></li>
  <li><a href="#模型比较分析添加vs不添加industry_type11" id="toc-模型比较分析添加vs不添加industry_type11" class="nav-link" data-scroll-target="#模型比较分析添加vs不添加industry_type11">模型比较分析：添加vs不添加industry_type11</a>
  <ul class="collapse">
  <li><a href="#第一步基准模型不包含行业分类" id="toc-第一步基准模型不包含行业分类" class="nav-link" data-scroll-target="#第一步基准模型不包含行业分类">第一步：基准模型（不包含行业分类）</a></li>
  <li><a href="#第二步包含行业固定效应的对比模型" id="toc-第二步包含行业固定效应的对比模型" class="nav-link" data-scroll-target="#第二步包含行业固定效应的对比模型">第二步：包含行业固定效应的对比模型</a></li>
  <li><a href="#第三步包含行业随机效应的对比模型" id="toc-第三步包含行业随机效应的对比模型" class="nav-link" data-scroll-target="#第三步包含行业随机效应的对比模型">第三步：包含行业随机效应的对比模型</a></li>
  <li><a href="#第四步模型性能指标计算" id="toc-第四步模型性能指标计算" class="nav-link" data-scroll-target="#第四步模型性能指标计算">第四步：模型性能指标计算</a></li>
  <li><a href="#第五步似然比检验" id="toc-第五步似然比检验" class="nav-link" data-scroll-target="#第五步似然比检验">第五步：似然比检验</a></li>
  <li><a href="#第六步创建模型比较汇总表格" id="toc-第六步创建模型比较汇总表格" class="nav-link" data-scroll-target="#第六步创建模型比较汇总表格">第六步：创建模型比较汇总表格</a></li>
  <li><a href="#第七步结果分析和可视化" id="toc-第七步结果分析和可视化" class="nav-link" data-scroll-target="#第七步结果分析和可视化">第七步：结果分析和可视化</a></li>
  <li><a href="#第八步详细结论分析" id="toc-第八步详细结论分析" class="nav-link" data-scroll-target="#第八步详细结论分析">第八步：详细结论分析</a></li>
  </ul></li>
  <li><a href="#原始混合效应模型-lmer---保留原有分析" id="toc-原始混合效应模型-lmer---保留原有分析" class="nav-link" data-scroll-target="#原始混合效应模型-lmer---保留原有分析">原始混合效应模型 (LMER) - 保留原有分析</a>
  <ul class="collapse">
  <li><a href="#基础混合效应模型" id="toc-基础混合效应模型" class="nav-link" data-scroll-target="#基础混合效应模型">基础混合效应模型</a></li>
  <li><a href="#包含行业随机效应的混合效应模型" id="toc-包含行业随机效应的混合效应模型" class="nav-link" data-scroll-target="#包含行业随机效应的混合效应模型">包含行业随机效应的混合效应模型</a></li>
  <li><a href="#包含行业固定效应的混合效应模型" id="toc-包含行业固定效应的混合效应模型" class="nav-link" data-scroll-target="#包含行业固定效应的混合效应模型">包含行业固定效应的混合效应模型</a></li>
  </ul></li>
  </ul>
</nav>
</div>
<main class="content" id="quarto-document-content">

<header id="title-block-header" class="quarto-title-block default">
<div class="quarto-title">
<h1 class="title">11类行业分类回归分析</h1>
</div>



<div class="quarto-title-meta">

    <div>
    <div class="quarto-title-meta-heading">Author</div>
    <div class="quarto-title-meta-contents">
             <p>ESG China Research </p>
          </div>
  </div>
    
    <div>
    <div class="quarto-title-meta-heading">Published</div>
    <div class="quarto-title-meta-contents">
      <p class="date">August 10, 2025</p>
    </div>
  </div>
  
    
  </div>
  


</header>


<section id="数据准备" class="level1">
<h1>数据准备</h1>
<section id="加载必要的库和数据" class="level2">
<h2 class="anchored" data-anchor-id="加载必要的库和数据">加载必要的库和数据</h2>
<div class="cell">
<div class="sourceCode cell-code" id="cb1"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 加载必要的库</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(plm)</span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lme4)</span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lmerTest)</span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(stargazer)</span>
<span id="cb1-6"><a href="#cb1-6" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(sjPlot)</span>
<span id="cb1-7"><a href="#cb1-7" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(dplyr)</span>
<span id="cb1-8"><a href="#cb1-8" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(knitr)</span>
<span id="cb1-9"><a href="#cb1-9" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(kableExtra)</span>
<span id="cb1-10"><a href="#cb1-10" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(stringr)</span>
<span id="cb1-11"><a href="#cb1-11" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-12"><a href="#cb1-12" aria-hidden="true" tabindex="-1"></a><span class="co"># 加载数据</span></span>
<span id="cb1-13"><a href="#cb1-13" aria-hidden="true" tabindex="-1"></a><span class="fu">load</span>(<span class="st">"dta1_20240903.RData"</span>)</span>
<span id="cb1-14"><a href="#cb1-14" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-15"><a href="#cb1-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-16"><a href="#cb1-16" aria-hidden="true" tabindex="-1"></a><span class="co"># 重新计算Age变量，使其随年份变化</span></span>
<span id="cb1-17"><a href="#cb1-17" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(lubridate)</span>
<span id="cb1-18"><a href="#cb1-18" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(dplyr)</span>
<span id="cb1-19"><a href="#cb1-19" aria-hidden="true" tabindex="-1"></a>dta1 <span class="ot">&lt;-</span> dta1 <span class="sc">%&gt;%</span></span>
<span id="cb1-20"><a href="#cb1-20" aria-hidden="true" tabindex="-1"></a>  <span class="fu">mutate</span>(</span>
<span id="cb1-21"><a href="#cb1-21" aria-hidden="true" tabindex="-1"></a>    <span class="at">EndYear =</span> <span class="fu">ymd</span>(EndYear),</span>
<span id="cb1-22"><a href="#cb1-22" aria-hidden="true" tabindex="-1"></a>    <span class="at">Year =</span> <span class="fu">year</span>(EndYear),</span>
<span id="cb1-23"><a href="#cb1-23" aria-hidden="true" tabindex="-1"></a>    <span class="at">Age =</span> Year <span class="sc">-</span> EstablishYear</span>
<span id="cb1-24"><a href="#cb1-24" aria-hidden="true" tabindex="-1"></a>  )</span>
<span id="cb1-25"><a href="#cb1-25" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-26"><a href="#cb1-26" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-27"><a href="#cb1-27" aria-hidden="true" tabindex="-1"></a><span class="co"># 检查数据结构</span></span>
<span id="cb1-28"><a href="#cb1-28" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"原始数据概览:</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>原始数据概览:</code></pre>
</div>
<div class="sourceCode cell-code" id="cb3"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"总观测数:"</span>, <span class="fu">nrow</span>(dta1), <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>总观测数: 54703 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb5"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"唯一公司数:"</span>, <span class="fu">length</span>(<span class="fu">unique</span>(dta1<span class="sc">$</span>Symbol)), <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>唯一公司数: 4973 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb7"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb7-1"><a href="#cb7-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"原始行业数:"</span>, <span class="fu">length</span>(<span class="fu">unique</span>(dta1<span class="sc">$</span>IndustryName)), <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>原始行业数: 85 </code></pre>
</div>
</div>
</section>
<section id="创建11类行业分类" class="level2">
<h2 class="anchored" data-anchor-id="创建11类行业分类">创建11类行业分类</h2>
<div class="cell">
<div class="sourceCode cell-code" id="cb9"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 创建11类行业分类</span></span>
<span id="cb9-2"><a href="#cb9-2" aria-hidden="true" tabindex="-1"></a>dta1<span class="sc">$</span>industry_type11 <span class="ot">&lt;-</span> <span class="fu">case_when</span>(</span>
<span id="cb9-3"><a href="#cb9-3" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 1 Energy</span></span>
<span id="cb9-4"><a href="#cb9-4" aria-hidden="true" tabindex="-1"></a>  dta1<span class="sc">$</span>IndustryName <span class="sc">%in%</span> <span class="fu">c</span>(<span class="st">"石油和天然气开采业"</span>,</span>
<span id="cb9-5"><a href="#cb9-5" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"石油加工、炼焦及核燃料加工业"</span>, <span class="st">"开发辅助活动"</span>, </span>
<span id="cb9-6"><a href="#cb9-6" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"煤炭开采和洗选业"</span>)                                   <span class="sc">~</span> <span class="st">"Energy"</span>,</span>
<span id="cb9-7"><a href="#cb9-7" aria-hidden="true" tabindex="-1"></a>  </span>
<span id="cb9-8"><a href="#cb9-8" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 2 Materials</span></span>
<span id="cb9-9"><a href="#cb9-9" aria-hidden="true" tabindex="-1"></a>  dta1<span class="sc">$</span>IndustryName <span class="sc">%in%</span> <span class="fu">c</span>(<span class="st">"化学原料及化学制品制造业"</span>,<span class="st">"化学纤维制造业"</span>,</span>
<span id="cb9-10"><a href="#cb9-10" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"黑色金属冶炼及压延加工业"</span>,<span class="st">"有色金属冶炼及压延加工业"</span>,</span>
<span id="cb9-11"><a href="#cb9-11" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"黑色金属矿采选业"</span>,<span class="st">"有色金属矿采选业"</span>,<span class="st">"非金属矿采选业"</span>,</span>
<span id="cb9-12"><a href="#cb9-12" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"非金属矿物制品业"</span>,<span class="st">"橡胶和塑料制品业"</span>,<span class="st">"造纸及纸制品业"</span>,</span>
<span id="cb9-13"><a href="#cb9-13" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"木材加工及木、竹、藤、棕、草制品业"</span>, <span class="st">"开采辅助活动"</span>, <span class="st">"林业"</span>,</span>
<span id="cb9-14"><a href="#cb9-14" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"金属制品业"</span>)                                         <span class="sc">~</span> <span class="st">"Materials"</span>,</span>
<span id="cb9-15"><a href="#cb9-15" aria-hidden="true" tabindex="-1"></a>  </span>
<span id="cb9-16"><a href="#cb9-16" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 3 Industrials</span></span>
<span id="cb9-17"><a href="#cb9-17" aria-hidden="true" tabindex="-1"></a>  dta1<span class="sc">$</span>IndustryName <span class="sc">%in%</span> <span class="fu">c</span>(<span class="st">"铁路、船舶、航空航天和其它运输设备制造业"</span>,<span class="st">"专用设备制造业"</span>,</span>
<span id="cb9-18"><a href="#cb9-18" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"电气机械及器材制造业"</span>,<span class="st">"通用设备制造业"</span>,</span>
<span id="cb9-19"><a href="#cb9-19" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"装卸搬运和运输代理业"</span>,<span class="st">"道路运输业"</span>,<span class="st">"水上运输业"</span>,</span>
<span id="cb9-20"><a href="#cb9-20" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"航空运输业"</span>,<span class="st">"铁路运输业"</span>,<span class="st">"仓储业"</span>,<span class="st">"其他制造业"</span>,</span>
<span id="cb9-21"><a href="#cb9-21" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"专业技术服务业"</span>,<span class="st">"其他服务业"</span>,</span>
<span id="cb9-22"><a href="#cb9-22" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"废弃资源综合利用业"</span>, <span class="st">"综合"</span>, <span class="st">"金属制品、机械和设备修理业"</span>,</span>
<span id="cb9-23"><a href="#cb9-23" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"邮政业"</span>, <span class="st">"仪器仪表制造业"</span>,</span>
<span id="cb9-24"><a href="#cb9-24" aria-hidden="true" tabindex="-1"></a>                           <span class="co"># 建筑／环保</span></span>
<span id="cb9-25"><a href="#cb9-25" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"房屋建筑业"</span>,<span class="st">"建筑安装业"</span>,<span class="st">"土木工程建筑业"</span>,</span>
<span id="cb9-26"><a href="#cb9-26" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"建筑装饰和其他建筑业"</span>,<span class="st">"生态保护和环境治理业"</span>,</span>
<span id="cb9-27"><a href="#cb9-27" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"公共设施管理业"</span>)                                     <span class="sc">~</span> <span class="st">"Industrials"</span>,</span>
<span id="cb9-28"><a href="#cb9-28" aria-hidden="true" tabindex="-1"></a>  </span>
<span id="cb9-29"><a href="#cb9-29" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 4 Consumer Discretionary</span></span>
<span id="cb9-30"><a href="#cb9-30" aria-hidden="true" tabindex="-1"></a>  dta1<span class="sc">$</span>IndustryName <span class="sc">%in%</span> <span class="fu">c</span>(<span class="st">"住宿业"</span>,<span class="st">"餐饮业"</span>,<span class="st">"体育"</span>,<span class="st">"居民服务业"</span>,<span class="st">"批发业"</span>,</span>
<span id="cb9-31"><a href="#cb9-31" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"教育"</span>,<span class="st">"纺织业"</span>,<span class="st">"零售业"</span>,<span class="st">"纺织服装、服饰业"</span>,</span>
<span id="cb9-32"><a href="#cb9-32" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"文教、工美、体育和娱乐用品制造业"</span>,<span class="st">"文化艺术业"</span>,</span>
<span id="cb9-33"><a href="#cb9-33" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"皮革、毛皮、羽毛及其制品和制鞋业"</span>,<span class="st">"家具制造业"</span>,</span>
<span id="cb9-34"><a href="#cb9-34" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"机动车、电子产品和日用产品修理业"</span>,</span>
<span id="cb9-35"><a href="#cb9-35" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"汽车制造业"</span>)                                         <span class="sc">~</span> <span class="st">"Consumer Discretionary"</span>,</span>
<span id="cb9-36"><a href="#cb9-36" aria-hidden="true" tabindex="-1"></a>  </span>
<span id="cb9-37"><a href="#cb9-37" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 5 Consumer Staples</span></span>
<span id="cb9-38"><a href="#cb9-38" aria-hidden="true" tabindex="-1"></a>  dta1<span class="sc">$</span>IndustryName <span class="sc">%in%</span> <span class="fu">c</span>(<span class="st">"酒、饮料和精制茶制造业"</span>,<span class="st">"食品制造业"</span>,<span class="st">"农副食品加工业"</span>,</span>
<span id="cb9-39"><a href="#cb9-39" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"农业"</span>,<span class="st">"畜牧业"</span>,<span class="st">"渔业"</span>,</span>
<span id="cb9-40"><a href="#cb9-40" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"农、林、牧、渔服务业"</span>)                               <span class="sc">~</span> <span class="st">"Consumer Staples"</span>,</span>
<span id="cb9-41"><a href="#cb9-41" aria-hidden="true" tabindex="-1"></a>  </span>
<span id="cb9-42"><a href="#cb9-42" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 6 Health Care</span></span>
<span id="cb9-43"><a href="#cb9-43" aria-hidden="true" tabindex="-1"></a>  dta1<span class="sc">$</span>IndustryName <span class="sc">%in%</span> <span class="fu">c</span>(<span class="st">"医药制造业"</span>,<span class="st">"卫生"</span>)                                 <span class="sc">~</span> <span class="st">"Health Care"</span>,</span>
<span id="cb9-44"><a href="#cb9-44" aria-hidden="true" tabindex="-1"></a>  </span>
<span id="cb9-45"><a href="#cb9-45" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 7 Financials</span></span>
<span id="cb9-46"><a href="#cb9-46" aria-hidden="true" tabindex="-1"></a>  dta1<span class="sc">$</span>IndustryName <span class="sc">%in%</span> <span class="fu">c</span>(<span class="st">"货币金融服务"</span>,<span class="st">"资本市场服务"</span>,<span class="st">"保险业"</span>,</span>
<span id="cb9-47"><a href="#cb9-47" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"其他金融业"</span>,<span class="st">"租赁业"</span>,<span class="st">"商务服务业"</span>)                   <span class="sc">~</span> <span class="st">"Financials"</span>,</span>
<span id="cb9-48"><a href="#cb9-48" aria-hidden="true" tabindex="-1"></a>  </span>
<span id="cb9-49"><a href="#cb9-49" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 8 Information Technology</span></span>
<span id="cb9-50"><a href="#cb9-50" aria-hidden="true" tabindex="-1"></a>  dta1<span class="sc">$</span>IndustryName <span class="sc">%in%</span> <span class="fu">c</span>(<span class="st">"软件和信息技术服务业"</span>,</span>
<span id="cb9-51"><a href="#cb9-51" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"计算机、通信和其他电子设备制造业"</span>,</span>
<span id="cb9-52"><a href="#cb9-52" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"印刷和记录媒介复制业"</span>,</span>
<span id="cb9-53"><a href="#cb9-53" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"研究和试验发展"</span>,<span class="st">"科技推广和应用服务业"</span>)               <span class="sc">~</span> <span class="st">"Information Technology"</span>,</span>
<span id="cb9-54"><a href="#cb9-54" aria-hidden="true" tabindex="-1"></a>  </span>
<span id="cb9-55"><a href="#cb9-55" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 9 Communication Services</span></span>
<span id="cb9-56"><a href="#cb9-56" aria-hidden="true" tabindex="-1"></a>  dta1<span class="sc">$</span>IndustryName <span class="sc">%in%</span> <span class="fu">c</span>(<span class="st">"电信、广播电视和卫星传输服务"</span>,</span>
<span id="cb9-57"><a href="#cb9-57" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"广播、电视、电影和影视录音制作业"</span>,</span>
<span id="cb9-58"><a href="#cb9-58" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"新闻和出版业"</span>,<span class="st">"互联网和相关服务"</span>)                     <span class="sc">~</span> <span class="st">"Communication Services"</span>,</span>
<span id="cb9-59"><a href="#cb9-59" aria-hidden="true" tabindex="-1"></a>  </span>
<span id="cb9-60"><a href="#cb9-60" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 10 Utilities</span></span>
<span id="cb9-61"><a href="#cb9-61" aria-hidden="true" tabindex="-1"></a>  dta1<span class="sc">$</span>IndustryName <span class="sc">%in%</span> <span class="fu">c</span>(<span class="st">"电力、热力生产和供应业"</span>,</span>
<span id="cb9-62"><a href="#cb9-62" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"燃气生产和供应业"</span>,</span>
<span id="cb9-63"><a href="#cb9-63" aria-hidden="true" tabindex="-1"></a>                           <span class="st">"水的生产和供应业"</span>)                                   <span class="sc">~</span> <span class="st">"Utilities"</span>,</span>
<span id="cb9-64"><a href="#cb9-64" aria-hidden="true" tabindex="-1"></a>  </span>
<span id="cb9-65"><a href="#cb9-65" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 11 Real Estate</span></span>
<span id="cb9-66"><a href="#cb9-66" aria-hidden="true" tabindex="-1"></a>  dta1<span class="sc">$</span>IndustryName <span class="sc">%in%</span> <span class="fu">c</span>(<span class="st">"房地产业"</span>)                            <span class="sc">~</span> <span class="st">"Real Estate"</span>,</span>
<span id="cb9-67"><a href="#cb9-67" aria-hidden="true" tabindex="-1"></a>  </span>
<span id="cb9-68"><a href="#cb9-68" aria-hidden="true" tabindex="-1"></a>  <span class="cn">TRUE</span> <span class="sc">~</span> <span class="cn">NA_character_</span></span>
<span id="cb9-69"><a href="#cb9-69" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb9-70"><a href="#cb9-70" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-71"><a href="#cb9-71" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-72"><a href="#cb9-72" aria-hidden="true" tabindex="-1"></a><span class="co"># 显示分类结果</span></span>
<span id="cb9-73"><a href="#cb9-73" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"11类分类结果:</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>11类分类结果:</code></pre>
</div>
<div class="sourceCode cell-code" id="cb11"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb11-1"><a href="#cb11-1" aria-hidden="true" tabindex="-1"></a>industry_table <span class="ot">&lt;-</span> <span class="fu">table</span>(dta1<span class="sc">$</span>industry_type11, <span class="at">useNA =</span> <span class="st">"always"</span>)</span>
<span id="cb11-2"><a href="#cb11-2" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(industry_table)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
Communication Services Consumer Discretionary       Consumer Staples 
                   969                   4286                   1742 
                Energy             Financials            Health Care 
                   507                   1232                   2220 
           Industrials Information Technology              Materials 
                  9801                   5379                   6884 
           Real Estate              Utilities                   &lt;NA&gt; 
                  1332                   1071                  19280 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb13"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb13-1"><a href="#cb13-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 分析NA值的详细情况</span></span>
<span id="cb13-2"><a href="#cb13-2" aria-hidden="true" tabindex="-1"></a>na_data <span class="ot">&lt;-</span> dta1 <span class="sc">%&gt;%</span> <span class="fu">filter</span>(<span class="fu">is.na</span>(industry_type11))</span>
<span id="cb13-3"><a href="#cb13-3" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"</span><span class="sc">\n</span><span class="st">=== NA值分析 ===</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
=== NA值分析 ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb15"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb15-1"><a href="#cb15-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"NA观测数:"</span>, <span class="fu">nrow</span>(na_data), <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>NA观测数: 19280 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb17"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb17-1"><a href="#cb17-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"NA占总观测数的比例:"</span>, <span class="fu">round</span>(<span class="fu">nrow</span>(na_data)<span class="sc">/</span><span class="fu">nrow</span>(dta1)<span class="sc">*</span><span class="dv">100</span>, <span class="dv">2</span>), <span class="st">"%</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>NA占总观测数的比例: 35.24 %</code></pre>
</div>
<div class="sourceCode cell-code" id="cb19"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb19-1"><a href="#cb19-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"NA涉及的唯一公司数:"</span>, <span class="fu">length</span>(<span class="fu">unique</span>(na_data<span class="sc">$</span>Symbol)), <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>NA涉及的唯一公司数: 2737 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb21"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb21-1"><a href="#cb21-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"NA涉及的唯一行业数:"</span>, <span class="fu">length</span>(<span class="fu">unique</span>(na_data<span class="sc">$</span>IndustryName)), <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>NA涉及的唯一行业数: 1 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb23"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb23-1"><a href="#cb23-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 显示所有未分类的行业名称</span></span>
<span id="cb23-2"><a href="#cb23-2" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"</span><span class="sc">\n</span><span class="st">未分类的行业名称:</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
未分类的行业名称:</code></pre>
</div>
<div class="sourceCode cell-code" id="cb25"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb25-1"><a href="#cb25-1" aria-hidden="true" tabindex="-1"></a>na_industries <span class="ot">&lt;-</span> na_data <span class="sc">%&gt;%</span></span>
<span id="cb25-2"><a href="#cb25-2" aria-hidden="true" tabindex="-1"></a>  <span class="fu">group_by</span>(IndustryName) <span class="sc">%&gt;%</span></span>
<span id="cb25-3"><a href="#cb25-3" aria-hidden="true" tabindex="-1"></a>  <span class="fu">summarise</span>(<span class="at">count =</span> <span class="fu">n</span>(), <span class="at">.groups =</span> <span class="st">'drop'</span>) <span class="sc">%&gt;%</span></span>
<span id="cb25-4"><a href="#cb25-4" aria-hidden="true" tabindex="-1"></a>  <span class="fu">arrange</span>(<span class="fu">desc</span>(count))</span>
<span id="cb25-5"><a href="#cb25-5" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(na_industries)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code># A tibble: 1 × 2
  IndustryName count
  &lt;chr&gt;        &lt;int&gt;
1 &lt;NA&gt;         19280</code></pre>
</div>
<div class="sourceCode cell-code" id="cb27"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb27-1"><a href="#cb27-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 创建去除NA的数据集用于分析</span></span>
<span id="cb27-2"><a href="#cb27-2" aria-hidden="true" tabindex="-1"></a>dta1_11class <span class="ot">&lt;-</span> dta1 <span class="sc">%&gt;%</span> <span class="fu">filter</span>(<span class="sc">!</span><span class="fu">is.na</span>(industry_type11))</span>
<span id="cb27-3"><a href="#cb27-3" aria-hidden="true" tabindex="-1"></a>dta1_11class <span class="ot">&lt;-</span> dta1</span>
<span id="cb27-4"><a href="#cb27-4" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"</span><span class="sc">\n</span><span class="st">去除NA后的观测数:"</span>, <span class="fu">nrow</span>(dta1_11class), <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
去除NA后的观测数: 54703 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb29"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb29-1"><a href="#cb29-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"去除NA后的公司数:"</span>, <span class="fu">length</span>(<span class="fu">unique</span>(dta1_11class<span class="sc">$</span>Symbol)), <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>去除NA后的公司数: 4973 </code></pre>
</div>
</div>
</section>
<section id="na值进一步分类方案" class="level2">
<h2 class="anchored" data-anchor-id="na值进一步分类方案">NA值进一步分类方案</h2>
<div class="cell">
<div class="sourceCode cell-code" id="cb31"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb31-1"><a href="#cb31-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 对NA值进行进一步分类</span></span>
<span id="cb31-2"><a href="#cb31-2" aria-hidden="true" tabindex="-1"></a><span class="co"># 基于行业名称的关键词匹配进行补充分类</span></span>
<span id="cb31-3"><a href="#cb31-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb31-4"><a href="#cb31-4" aria-hidden="true" tabindex="-1"></a><span class="co"># 创建补充分类函数</span></span>
<span id="cb31-5"><a href="#cb31-5" aria-hidden="true" tabindex="-1"></a>classify_na_industries <span class="ot">&lt;-</span> <span class="cf">function</span>(industry_name) {</span>
<span id="cb31-6"><a href="#cb31-6" aria-hidden="true" tabindex="-1"></a>  <span class="fu">case_when</span>(</span>
<span id="cb31-7"><a href="#cb31-7" aria-hidden="true" tabindex="-1"></a>    <span class="co"># 检查是否包含特定关键词</span></span>
<span id="cb31-8"><a href="#cb31-8" aria-hidden="true" tabindex="-1"></a>    <span class="fu">str_detect</span>(industry_name, <span class="st">"金融|银行|证券|保险|投资|基金|信托|担保|租赁"</span>) <span class="sc">~</span> <span class="st">"Financials"</span>,</span>
<span id="cb31-9"><a href="#cb31-9" aria-hidden="true" tabindex="-1"></a>    <span class="fu">str_detect</span>(industry_name, <span class="st">"房地产|建筑|装修|装饰|土建|工程"</span>) <span class="sc">~</span> <span class="st">"Real Estate"</span>,</span>
<span id="cb31-10"><a href="#cb31-10" aria-hidden="true" tabindex="-1"></a>    <span class="fu">str_detect</span>(industry_name, <span class="st">"制造|生产|加工|机械|设备|器材|仪器"</span>) <span class="sc">~</span> <span class="st">"Industrials"</span>,</span>
<span id="cb31-11"><a href="#cb31-11" aria-hidden="true" tabindex="-1"></a>    <span class="fu">str_detect</span>(industry_name, <span class="st">"化工|化学|材料|金属|矿物|钢铁|有色|塑料|橡胶"</span>) <span class="sc">~</span> <span class="st">"Materials"</span>,</span>
<span id="cb31-12"><a href="#cb31-12" aria-hidden="true" tabindex="-1"></a>    <span class="fu">str_detect</span>(industry_name, <span class="st">"电力|能源|燃气|石油|煤炭|新能源"</span>) <span class="sc">~</span> <span class="st">"Energy"</span>,</span>
<span id="cb31-13"><a href="#cb31-13" aria-hidden="true" tabindex="-1"></a>    <span class="fu">str_detect</span>(industry_name, <span class="st">"医药|医疗|卫生|健康|生物|制药"</span>) <span class="sc">~</span> <span class="st">"Health Care"</span>,</span>
<span id="cb31-14"><a href="#cb31-14" aria-hidden="true" tabindex="-1"></a>    <span class="fu">str_detect</span>(industry_name, <span class="st">"食品|农业|畜牧|渔业|林业|饮料"</span>) <span class="sc">~</span> <span class="st">"Consumer Staples"</span>,</span>
<span id="cb31-15"><a href="#cb31-15" aria-hidden="true" tabindex="-1"></a>    <span class="fu">str_detect</span>(industry_name, <span class="st">"零售|批发|商贸|消费|服装|纺织|家具|娱乐|旅游|酒店|餐饮"</span>) <span class="sc">~</span> <span class="st">"Consumer Discretionary"</span>,</span>
<span id="cb31-16"><a href="#cb31-16" aria-hidden="true" tabindex="-1"></a>    <span class="fu">str_detect</span>(industry_name, <span class="st">"软件|信息|技术|计算机|电子|通信|互联网|科技"</span>) <span class="sc">~</span> <span class="st">"Information Technology"</span>,</span>
<span id="cb31-17"><a href="#cb31-17" aria-hidden="true" tabindex="-1"></a>    <span class="fu">str_detect</span>(industry_name, <span class="st">"电信|广播|媒体|出版|传媒"</span>) <span class="sc">~</span> <span class="st">"Communication Services"</span>,</span>
<span id="cb31-18"><a href="#cb31-18" aria-hidden="true" tabindex="-1"></a>    <span class="fu">str_detect</span>(industry_name, <span class="st">"水务|环保|公用|供水|供气|垃圾|污水"</span>) <span class="sc">~</span> <span class="st">"Utilities"</span>,</span>
<span id="cb31-19"><a href="#cb31-19" aria-hidden="true" tabindex="-1"></a>    <span class="cn">TRUE</span> <span class="sc">~</span> <span class="st">"Other"</span>  <span class="co"># 仍然无法分类的归为Other</span></span>
<span id="cb31-20"><a href="#cb31-20" aria-hidden="true" tabindex="-1"></a>  )</span>
<span id="cb31-21"><a href="#cb31-21" aria-hidden="true" tabindex="-1"></a>}</span>
<span id="cb31-22"><a href="#cb31-22" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb31-23"><a href="#cb31-23" aria-hidden="true" tabindex="-1"></a><span class="co"># 应用补充分类</span></span>
<span id="cb31-24"><a href="#cb31-24" aria-hidden="true" tabindex="-1"></a>na_data_classified <span class="ot">&lt;-</span> na_data <span class="sc">%&gt;%</span></span>
<span id="cb31-25"><a href="#cb31-25" aria-hidden="true" tabindex="-1"></a>  <span class="fu">mutate</span>(</span>
<span id="cb31-26"><a href="#cb31-26" aria-hidden="true" tabindex="-1"></a>    <span class="at">suggested_classification =</span> <span class="fu">classify_na_industries</span>(IndustryName),</span>
<span id="cb31-27"><a href="#cb31-27" aria-hidden="true" tabindex="-1"></a>    <span class="at">classification_confidence =</span> <span class="fu">case_when</span>(</span>
<span id="cb31-28"><a href="#cb31-28" aria-hidden="true" tabindex="-1"></a>      suggested_classification <span class="sc">==</span> <span class="st">"Other"</span> <span class="sc">~</span> <span class="st">"Low"</span>,</span>
<span id="cb31-29"><a href="#cb31-29" aria-hidden="true" tabindex="-1"></a>      <span class="cn">TRUE</span> <span class="sc">~</span> <span class="st">"Medium"</span></span>
<span id="cb31-30"><a href="#cb31-30" aria-hidden="true" tabindex="-1"></a>    )</span>
<span id="cb31-31"><a href="#cb31-31" aria-hidden="true" tabindex="-1"></a>  )</span>
<span id="cb31-32"><a href="#cb31-32" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb31-33"><a href="#cb31-33" aria-hidden="true" tabindex="-1"></a><span class="co"># 显示补充分类结果</span></span>
<span id="cb31-34"><a href="#cb31-34" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"=== 补充分类结果 ===</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>=== 补充分类结果 ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb33"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb33-1"><a href="#cb33-1" aria-hidden="true" tabindex="-1"></a>classification_summary <span class="ot">&lt;-</span> na_data_classified <span class="sc">%&gt;%</span></span>
<span id="cb33-2"><a href="#cb33-2" aria-hidden="true" tabindex="-1"></a>  <span class="fu">group_by</span>(suggested_classification) <span class="sc">%&gt;%</span></span>
<span id="cb33-3"><a href="#cb33-3" aria-hidden="true" tabindex="-1"></a>  <span class="fu">summarise</span>(</span>
<span id="cb33-4"><a href="#cb33-4" aria-hidden="true" tabindex="-1"></a>    <span class="at">count =</span> <span class="fu">n</span>(),</span>
<span id="cb33-5"><a href="#cb33-5" aria-hidden="true" tabindex="-1"></a>    <span class="at">unique_companies =</span> <span class="fu">length</span>(<span class="fu">unique</span>(Symbol)),</span>
<span id="cb33-6"><a href="#cb33-6" aria-hidden="true" tabindex="-1"></a>    <span class="at">unique_industries =</span> <span class="fu">length</span>(<span class="fu">unique</span>(IndustryName)),</span>
<span id="cb33-7"><a href="#cb33-7" aria-hidden="true" tabindex="-1"></a>    <span class="at">.groups =</span> <span class="st">'drop'</span></span>
<span id="cb33-8"><a href="#cb33-8" aria-hidden="true" tabindex="-1"></a>  ) <span class="sc">%&gt;%</span></span>
<span id="cb33-9"><a href="#cb33-9" aria-hidden="true" tabindex="-1"></a>  <span class="fu">arrange</span>(<span class="fu">desc</span>(count))</span>
<span id="cb33-10"><a href="#cb33-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb33-11"><a href="#cb33-11" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(classification_summary)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code># A tibble: 1 × 4
  suggested_classification count unique_companies unique_industries
  &lt;chr&gt;                    &lt;int&gt;            &lt;int&gt;             &lt;int&gt;
1 Other                    19280             2737                 1</code></pre>
</div>
<div class="sourceCode cell-code" id="cb35"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb35-1"><a href="#cb35-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 显示每个补充分类中的具体行业</span></span>
<span id="cb35-2"><a href="#cb35-2" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"</span><span class="sc">\n</span><span class="st">=== 各分类中的具体行业 ===</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
=== 各分类中的具体行业 ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb37"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb37-1"><a href="#cb37-1" aria-hidden="true" tabindex="-1"></a><span class="cf">for</span>(class <span class="cf">in</span> <span class="fu">unique</span>(classification_summary<span class="sc">$</span>suggested_classification)) {</span>
<span id="cb37-2"><a href="#cb37-2" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">"</span><span class="sc">\n</span><span class="st">"</span>, class, <span class="st">":</span><span class="sc">\n</span><span class="st">"</span>)</span>
<span id="cb37-3"><a href="#cb37-3" aria-hidden="true" tabindex="-1"></a>  industries_in_class <span class="ot">&lt;-</span> na_data_classified <span class="sc">%&gt;%</span></span>
<span id="cb37-4"><a href="#cb37-4" aria-hidden="true" tabindex="-1"></a>    <span class="fu">filter</span>(suggested_classification <span class="sc">==</span> class) <span class="sc">%&gt;%</span></span>
<span id="cb37-5"><a href="#cb37-5" aria-hidden="true" tabindex="-1"></a>    <span class="fu">group_by</span>(IndustryName) <span class="sc">%&gt;%</span></span>
<span id="cb37-6"><a href="#cb37-6" aria-hidden="true" tabindex="-1"></a>    <span class="fu">summarise</span>(<span class="at">count =</span> <span class="fu">n</span>(), <span class="at">.groups =</span> <span class="st">'drop'</span>) <span class="sc">%&gt;%</span></span>
<span id="cb37-7"><a href="#cb37-7" aria-hidden="true" tabindex="-1"></a>    <span class="fu">arrange</span>(<span class="fu">desc</span>(count))</span>
<span id="cb37-8"><a href="#cb37-8" aria-hidden="true" tabindex="-1"></a>  <span class="fu">print</span>(industries_in_class)</span>
<span id="cb37-9"><a href="#cb37-9" aria-hidden="true" tabindex="-1"></a>}</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
 Other :
# A tibble: 1 × 2
  IndustryName count
  &lt;chr&gt;        &lt;int&gt;
1 &lt;NA&gt;         19280</code></pre>
</div>
</div>
</section>
<section id="创建完整的12类分类包含other类" class="level2">
<h2 class="anchored" data-anchor-id="创建完整的12类分类包含other类">创建完整的12类分类（包含Other类）</h2>
<div class="cell">
<div class="sourceCode cell-code" id="cb39"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb39-1"><a href="#cb39-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 创建包含补充分类的完整数据集</span></span>
<span id="cb39-2"><a href="#cb39-2" aria-hidden="true" tabindex="-1"></a>dta1_12class <span class="ot">&lt;-</span> dta1 <span class="sc">%&gt;%</span></span>
<span id="cb39-3"><a href="#cb39-3" aria-hidden="true" tabindex="-1"></a>  <span class="fu">mutate</span>(</span>
<span id="cb39-4"><a href="#cb39-4" aria-hidden="true" tabindex="-1"></a>    <span class="at">industry_type12 =</span> <span class="fu">case_when</span>(</span>
<span id="cb39-5"><a href="#cb39-5" aria-hidden="true" tabindex="-1"></a>      <span class="sc">!</span><span class="fu">is.na</span>(industry_type11) <span class="sc">~</span> industry_type11,</span>
<span id="cb39-6"><a href="#cb39-6" aria-hidden="true" tabindex="-1"></a>      <span class="fu">is.na</span>(industry_type11) <span class="sc">~</span> <span class="fu">classify_na_industries</span>(IndustryName)</span>
<span id="cb39-7"><a href="#cb39-7" aria-hidden="true" tabindex="-1"></a>    )</span>
<span id="cb39-8"><a href="#cb39-8" aria-hidden="true" tabindex="-1"></a>  )</span>
<span id="cb39-9"><a href="#cb39-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb39-10"><a href="#cb39-10" aria-hidden="true" tabindex="-1"></a><span class="co"># 显示12类分类结果</span></span>
<span id="cb39-11"><a href="#cb39-11" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"=== 12类分类结果（包含Other） ===</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>=== 12类分类结果（包含Other） ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb41"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb41-1"><a href="#cb41-1" aria-hidden="true" tabindex="-1"></a>industry_table_12 <span class="ot">&lt;-</span> <span class="fu">table</span>(dta1_12class<span class="sc">$</span>industry_type12, <span class="at">useNA =</span> <span class="st">"always"</span>)</span>
<span id="cb41-2"><a href="#cb41-2" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(industry_table_12)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
Communication Services Consumer Discretionary       Consumer Staples 
                   969                   4286                   1742 
                Energy             Financials            Health Care 
                   507                   1232                   2220 
           Industrials Information Technology              Materials 
                  9801                   5379                   6884 
                 Other            Real Estate              Utilities 
                 19280                   1332                   1071 
                  &lt;NA&gt; 
                     0 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb43"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb43-1"><a href="#cb43-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 计算分类覆盖率</span></span>
<span id="cb43-2"><a href="#cb43-2" aria-hidden="true" tabindex="-1"></a>coverage_stats <span class="ot">&lt;-</span> dta1_12class <span class="sc">%&gt;%</span></span>
<span id="cb43-3"><a href="#cb43-3" aria-hidden="true" tabindex="-1"></a>  <span class="fu">summarise</span>(</span>
<span id="cb43-4"><a href="#cb43-4" aria-hidden="true" tabindex="-1"></a>    <span class="at">total_obs =</span> <span class="fu">n</span>(),</span>
<span id="cb43-5"><a href="#cb43-5" aria-hidden="true" tabindex="-1"></a>    <span class="at">classified_obs =</span> <span class="fu">sum</span>(<span class="sc">!</span><span class="fu">is.na</span>(industry_type12) <span class="sc">&amp;</span> industry_type12 <span class="sc">!=</span> <span class="st">"Other"</span>),</span>
<span id="cb43-6"><a href="#cb43-6" aria-hidden="true" tabindex="-1"></a>    <span class="at">other_obs =</span> <span class="fu">sum</span>(industry_type12 <span class="sc">==</span> <span class="st">"Other"</span>, <span class="at">na.rm =</span> <span class="cn">TRUE</span>),</span>
<span id="cb43-7"><a href="#cb43-7" aria-hidden="true" tabindex="-1"></a>    <span class="at">still_na =</span> <span class="fu">sum</span>(<span class="fu">is.na</span>(industry_type12)),</span>
<span id="cb43-8"><a href="#cb43-8" aria-hidden="true" tabindex="-1"></a>    <span class="at">coverage_rate =</span> <span class="fu">round</span>(classified_obs <span class="sc">/</span> total_obs <span class="sc">*</span> <span class="dv">100</span>, <span class="dv">2</span>),</span>
<span id="cb43-9"><a href="#cb43-9" aria-hidden="true" tabindex="-1"></a>    <span class="at">other_rate =</span> <span class="fu">round</span>(other_obs <span class="sc">/</span> total_obs <span class="sc">*</span> <span class="dv">100</span>, <span class="dv">2</span>)</span>
<span id="cb43-10"><a href="#cb43-10" aria-hidden="true" tabindex="-1"></a>  )</span>
<span id="cb43-11"><a href="#cb43-11" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb43-12"><a href="#cb43-12" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"</span><span class="sc">\n</span><span class="st">=== 分类覆盖率统计 ===</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
=== 分类覆盖率统计 ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb45"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb45-1"><a href="#cb45-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"总观测数:"</span>, coverage_stats<span class="sc">$</span>total_obs, <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>总观测数: 54703 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb47"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb47-1"><a href="#cb47-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"已分类观测数:"</span>, coverage_stats<span class="sc">$</span>classified_obs, <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>已分类观测数: 35423 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb49"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb49-1"><a href="#cb49-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"Other类观测数:"</span>, coverage_stats<span class="sc">$</span>other_obs, <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>Other类观测数: 19280 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb51"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb51-1"><a href="#cb51-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"仍为NA的观测数:"</span>, coverage_stats<span class="sc">$</span>still_na, <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>仍为NA的观测数: 0 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb53"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb53-1"><a href="#cb53-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"分类覆盖率:"</span>, coverage_stats<span class="sc">$</span>coverage_rate, <span class="st">"%</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>分类覆盖率: 64.76 %</code></pre>
</div>
<div class="sourceCode cell-code" id="cb55"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb55-1"><a href="#cb55-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"Other类占比:"</span>, coverage_stats<span class="sc">$</span>other_rate, <span class="st">"%</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>Other类占比: 35.24 %</code></pre>
</div>
<div class="sourceCode cell-code" id="cb57"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb57-1"><a href="#cb57-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 保存12类分类数据用于后续分析</span></span>
<span id="cb57-2"><a href="#cb57-2" aria-hidden="true" tabindex="-1"></a>dta1_12class_clean <span class="ot">&lt;-</span> dta1_12class <span class="sc">%&gt;%</span> <span class="fu">filter</span>(industry_type12 <span class="sc">!=</span> <span class="st">"Other"</span> <span class="sc">&amp;</span> <span class="sc">!</span><span class="fu">is.na</span>(industry_type12))</span>
<span id="cb57-3"><a href="#cb57-3" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"</span><span class="sc">\n</span><span class="st">最终用于分析的观测数:"</span>, <span class="fu">nrow</span>(dta1_12class_clean), <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
最终用于分析的观测数: 35423 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb59"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb59-1"><a href="#cb59-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"最终用于分析的公司数:"</span>, <span class="fu">length</span>(<span class="fu">unique</span>(dta1_12class_clean<span class="sc">$</span>Symbol)), <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>最终用于分析的公司数: 4754 </code></pre>
</div>
</div>
</section>
</section>
<section id="模型比较分析添加vs不添加industry_type11" class="level1">
<h1>模型比较分析：添加vs不添加industry_type11</h1>
<section id="第一步基准模型不包含行业分类" class="level2">
<h2 class="anchored" data-anchor-id="第一步基准模型不包含行业分类">第一步：基准模型（不包含行业分类）</h2>
<div class="cell">
<div class="sourceCode cell-code" id="cb61"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb61-1"><a href="#cb61-1" aria-hidden="true" tabindex="-1"></a><span class="co"># ===== 基准模型组：不包含任何行业效应 =====</span></span>
<span id="cb61-2"><a href="#cb61-2" aria-hidden="true" tabindex="-1"></a><span class="co"># 这些模型只包含省份/城市随机效应，作为比较的基准</span></span>
<span id="cb61-3"><a href="#cb61-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb61-4"><a href="#cb61-4" aria-hidden="true" tabindex="-1"></a><span class="co"># P3系列基准模型</span></span>
<span id="cb61-5"><a href="#cb61-5" aria-hidden="true" tabindex="-1"></a>baseline_p3_1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> connection_num <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span></span>
<span id="cb61-6"><a href="#cb61-6" aria-hidden="true" tabindex="-1"></a>                     RegisterCapital_log  <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb61-7"><a href="#cb61-7" aria-hidden="true" tabindex="-1"></a>                     <span class="at">data =</span> dta1_11class)</span>
<span id="cb61-8"><a href="#cb61-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb61-9"><a href="#cb61-9" aria-hidden="true" tabindex="-1"></a>baseline_p3_2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> after_first_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span></span>
<span id="cb61-10"><a href="#cb61-10" aria-hidden="true" tabindex="-1"></a>                     RegisterCapital_log  <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb61-11"><a href="#cb61-11" aria-hidden="true" tabindex="-1"></a>                     <span class="at">data =</span> dta1_11class)</span>
<span id="cb61-12"><a href="#cb61-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb61-13"><a href="#cb61-13" aria-hidden="true" tabindex="-1"></a>baseline_p3_3 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> after_first_inspection <span class="sc">*</span> connection_num <span class="sc">+</span></span>
<span id="cb61-14"><a href="#cb61-14" aria-hidden="true" tabindex="-1"></a>                     ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb61-15"><a href="#cb61-15" aria-hidden="true" tabindex="-1"></a>                     <span class="at">data =</span> dta1_11class)</span>
<span id="cb61-16"><a href="#cb61-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb61-17"><a href="#cb61-17" aria-hidden="true" tabindex="-1"></a><span class="co"># P4系列基准模型 - 中央政治关联</span></span>
<span id="cb61-18"><a href="#cb61-18" aria-hidden="true" tabindex="-1"></a>baseline_p4_central_1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> central_connection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span></span>
<span id="cb61-19"><a href="#cb61-19" aria-hidden="true" tabindex="-1"></a>                             RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb61-20"><a href="#cb61-20" aria-hidden="true" tabindex="-1"></a>                             <span class="at">data =</span> dta1_11class)</span>
<span id="cb61-21"><a href="#cb61-21" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb61-22"><a href="#cb61-22" aria-hidden="true" tabindex="-1"></a>baseline_p4_central_2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> central_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span></span>
<span id="cb61-23"><a href="#cb61-23" aria-hidden="true" tabindex="-1"></a>                             ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span></span>
<span id="cb61-24"><a href="#cb61-24" aria-hidden="true" tabindex="-1"></a>                             (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb61-25"><a href="#cb61-25" aria-hidden="true" tabindex="-1"></a>                             <span class="at">data =</span> dta1_11class)</span>
<span id="cb61-26"><a href="#cb61-26" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb61-27"><a href="#cb61-27" aria-hidden="true" tabindex="-1"></a><span class="co"># P4系列基准模型 - 地方政治关联</span></span>
<span id="cb61-28"><a href="#cb61-28" aria-hidden="true" tabindex="-1"></a>baseline_p4_local_1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> local_connection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span></span>
<span id="cb61-29"><a href="#cb61-29" aria-hidden="true" tabindex="-1"></a>                           RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb61-30"><a href="#cb61-30" aria-hidden="true" tabindex="-1"></a>                           <span class="at">data =</span> dta1_11class)</span>
<span id="cb61-31"><a href="#cb61-31" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb61-32"><a href="#cb61-32" aria-hidden="true" tabindex="-1"></a>baseline_p4_local_2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> local_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span></span>
<span id="cb61-33"><a href="#cb61-33" aria-hidden="true" tabindex="-1"></a>                           ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span></span>
<span id="cb61-34"><a href="#cb61-34" aria-hidden="true" tabindex="-1"></a>                           (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb61-35"><a href="#cb61-35" aria-hidden="true" tabindex="-1"></a>                           <span class="at">data =</span> dta1_11class)</span>
<span id="cb61-36"><a href="#cb61-36" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb61-37"><a href="#cb61-37" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"=== 基准模型创建完成 ===</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>=== 基准模型创建完成 ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb63"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb63-1"><a href="#cb63-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"已创建7个基准模型（不包含行业效应）</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>已创建7个基准模型（不包含行业效应）</code></pre>
</div>
</div>
</section>
<section id="第二步包含行业固定效应的对比模型" class="level2">
<h2 class="anchored" data-anchor-id="第二步包含行业固定效应的对比模型">第二步：包含行业固定效应的对比模型</h2>
<div class="cell">
<div class="sourceCode cell-code" id="cb65"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb65-1"><a href="#cb65-1" aria-hidden="true" tabindex="-1"></a><span class="co"># ===== 行业固定效应模型组 =====</span></span>
<span id="cb65-2"><a href="#cb65-2" aria-hidden="true" tabindex="-1"></a><span class="co"># 这些模型在基准模型基础上添加industry_type11作为固定效应</span></span>
<span id="cb65-3"><a href="#cb65-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb65-4"><a href="#cb65-4" aria-hidden="true" tabindex="-1"></a><span class="co"># P3系列 + 行业固定效应</span></span>
<span id="cb65-5"><a href="#cb65-5" aria-hidden="true" tabindex="-1"></a>fixed_p3_1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> connection_num <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span></span>
<span id="cb65-6"><a href="#cb65-6" aria-hidden="true" tabindex="-1"></a>                   RegisterCapital_log <span class="sc">+</span>   <span class="fu">as.factor</span>(industry_type11) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb65-7"><a href="#cb65-7" aria-hidden="true" tabindex="-1"></a>                   <span class="at">data =</span> dta1_11class)</span>
<span id="cb65-8"><a href="#cb65-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb65-9"><a href="#cb65-9" aria-hidden="true" tabindex="-1"></a>fixed_p3_2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> after_first_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span></span>
<span id="cb65-10"><a href="#cb65-10" aria-hidden="true" tabindex="-1"></a>                   RegisterCapital_log <span class="sc">+</span>   <span class="fu">as.factor</span>(industry_type11) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb65-11"><a href="#cb65-11" aria-hidden="true" tabindex="-1"></a>                   <span class="at">data =</span> dta1_11class)</span>
<span id="cb65-12"><a href="#cb65-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb65-13"><a href="#cb65-13" aria-hidden="true" tabindex="-1"></a>fixed_p3_3 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> after_first_inspection <span class="sc">*</span> connection_num <span class="sc">+</span></span>
<span id="cb65-14"><a href="#cb65-14" aria-hidden="true" tabindex="-1"></a>                   ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb65-15"><a href="#cb65-15" aria-hidden="true" tabindex="-1"></a>                   <span class="fu">as.factor</span>(industry_type11) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb65-16"><a href="#cb65-16" aria-hidden="true" tabindex="-1"></a>                   <span class="at">data =</span> dta1_11class)</span>
<span id="cb65-17"><a href="#cb65-17" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb65-18"><a href="#cb65-18" aria-hidden="true" tabindex="-1"></a><span class="co"># P4系列 + 行业固定效应 - 中央政治关联</span></span>
<span id="cb65-19"><a href="#cb65-19" aria-hidden="true" tabindex="-1"></a>fixed_p4_central_1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> central_connection <span class="sc">+</span></span>
<span id="cb65-20"><a href="#cb65-20" aria-hidden="true" tabindex="-1"></a>                          ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb65-21"><a href="#cb65-21" aria-hidden="true" tabindex="-1"></a>                          <span class="fu">as.factor</span>(industry_type11) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb65-22"><a href="#cb65-22" aria-hidden="true" tabindex="-1"></a>                          <span class="at">data =</span> dta1_11class)</span>
<span id="cb65-23"><a href="#cb65-23" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb65-24"><a href="#cb65-24" aria-hidden="true" tabindex="-1"></a>fixed_p4_central_2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> central_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span></span>
<span id="cb65-25"><a href="#cb65-25" aria-hidden="true" tabindex="-1"></a>                          ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb65-26"><a href="#cb65-26" aria-hidden="true" tabindex="-1"></a>                          <span class="fu">as.factor</span>(industry_type11) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb65-27"><a href="#cb65-27" aria-hidden="true" tabindex="-1"></a>                          <span class="at">data =</span> dta1_11class)</span>
<span id="cb65-28"><a href="#cb65-28" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb65-29"><a href="#cb65-29" aria-hidden="true" tabindex="-1"></a><span class="co"># P4系列 + 行业固定效应 - 地方政治关联</span></span>
<span id="cb65-30"><a href="#cb65-30" aria-hidden="true" tabindex="-1"></a>fixed_p4_local_1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> local_connection <span class="sc">+</span></span>
<span id="cb65-31"><a href="#cb65-31" aria-hidden="true" tabindex="-1"></a>                        ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb65-32"><a href="#cb65-32" aria-hidden="true" tabindex="-1"></a>                        <span class="fu">as.factor</span>(industry_type11) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb65-33"><a href="#cb65-33" aria-hidden="true" tabindex="-1"></a>                        <span class="at">data =</span> dta1_11class)</span>
<span id="cb65-34"><a href="#cb65-34" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb65-35"><a href="#cb65-35" aria-hidden="true" tabindex="-1"></a>fixed_p4_local_2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> local_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span></span>
<span id="cb65-36"><a href="#cb65-36" aria-hidden="true" tabindex="-1"></a>                        ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb65-37"><a href="#cb65-37" aria-hidden="true" tabindex="-1"></a>                        <span class="fu">as.factor</span>(industry_type11) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb65-38"><a href="#cb65-38" aria-hidden="true" tabindex="-1"></a>                        <span class="at">data =</span> dta1_11class)</span>
<span id="cb65-39"><a href="#cb65-39" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb65-40"><a href="#cb65-40" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"=== 行业固定效应模型创建完成 ===</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>=== 行业固定效应模型创建完成 ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb67"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb67-1"><a href="#cb67-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"已创建7个包含行业固定效应的模型</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>已创建7个包含行业固定效应的模型</code></pre>
</div>
</div>
</section>
<section id="第三步包含行业随机效应的对比模型" class="level2">
<h2 class="anchored" data-anchor-id="第三步包含行业随机效应的对比模型">第三步：包含行业随机效应的对比模型</h2>
<div class="cell">
<div class="sourceCode cell-code" id="cb69"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb69-1"><a href="#cb69-1" aria-hidden="true" tabindex="-1"></a><span class="co"># ===== 行业随机效应模型组 =====</span></span>
<span id="cb69-2"><a href="#cb69-2" aria-hidden="true" tabindex="-1"></a><span class="co"># 这些模型在基准模型基础上添加industry_type11作为随机效应</span></span>
<span id="cb69-3"><a href="#cb69-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb69-4"><a href="#cb69-4" aria-hidden="true" tabindex="-1"></a><span class="co"># P3系列 + 行业随机效应</span></span>
<span id="cb69-5"><a href="#cb69-5" aria-hidden="true" tabindex="-1"></a>random_p3_1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> connection_num <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span></span>
<span id="cb69-6"><a href="#cb69-6" aria-hidden="true" tabindex="-1"></a>                   RegisterCapital_log <span class="sc">+</span>   (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> industry_type11),</span>
<span id="cb69-7"><a href="#cb69-7" aria-hidden="true" tabindex="-1"></a>                   <span class="at">data =</span> dta1_11class)</span>
<span id="cb69-8"><a href="#cb69-8" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb69-9"><a href="#cb69-9" aria-hidden="true" tabindex="-1"></a>random_p3_2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> after_first_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span></span>
<span id="cb69-10"><a href="#cb69-10" aria-hidden="true" tabindex="-1"></a>                   RegisterCapital_log <span class="sc">+</span>   (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> industry_type11),</span>
<span id="cb69-11"><a href="#cb69-11" aria-hidden="true" tabindex="-1"></a>                   <span class="at">data =</span> dta1_11class)</span>
<span id="cb69-12"><a href="#cb69-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb69-13"><a href="#cb69-13" aria-hidden="true" tabindex="-1"></a>random_p3_3 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> after_first_inspection <span class="sc">*</span> connection_num <span class="sc">+</span></span>
<span id="cb69-14"><a href="#cb69-14" aria-hidden="true" tabindex="-1"></a>                   ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb69-15"><a href="#cb69-15" aria-hidden="true" tabindex="-1"></a>                   (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> industry_type11),</span>
<span id="cb69-16"><a href="#cb69-16" aria-hidden="true" tabindex="-1"></a>                   <span class="at">data =</span> dta1_11class)</span>
<span id="cb69-17"><a href="#cb69-17" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb69-18"><a href="#cb69-18" aria-hidden="true" tabindex="-1"></a><span class="co"># P4系列 + 行业随机效应 - 中央政治关联</span></span>
<span id="cb69-19"><a href="#cb69-19" aria-hidden="true" tabindex="-1"></a>random_p4_central_1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> central_connection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span></span>
<span id="cb69-20"><a href="#cb69-20" aria-hidden="true" tabindex="-1"></a>                           RegisterCapital_log <span class="sc">+</span>   (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> industry_type11),</span>
<span id="cb69-21"><a href="#cb69-21" aria-hidden="true" tabindex="-1"></a>                           <span class="at">data =</span> dta1_11class)</span>
<span id="cb69-22"><a href="#cb69-22" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb69-23"><a href="#cb69-23" aria-hidden="true" tabindex="-1"></a>random_p4_central_2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> central_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span></span>
<span id="cb69-24"><a href="#cb69-24" aria-hidden="true" tabindex="-1"></a>                           ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb69-25"><a href="#cb69-25" aria-hidden="true" tabindex="-1"></a>                           (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> industry_type11),</span>
<span id="cb69-26"><a href="#cb69-26" aria-hidden="true" tabindex="-1"></a>                           <span class="at">data =</span> dta1_11class)</span>
<span id="cb69-27"><a href="#cb69-27" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb69-28"><a href="#cb69-28" aria-hidden="true" tabindex="-1"></a><span class="co"># P4系列 + 行业随机效应 - 地方政治关联</span></span>
<span id="cb69-29"><a href="#cb69-29" aria-hidden="true" tabindex="-1"></a>random_p4_local_1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> local_connection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span></span>
<span id="cb69-30"><a href="#cb69-30" aria-hidden="true" tabindex="-1"></a>                         RegisterCapital_log <span class="sc">+</span>   (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> industry_type11),</span>
<span id="cb69-31"><a href="#cb69-31" aria-hidden="true" tabindex="-1"></a>                         <span class="at">data =</span> dta1_11class)</span>
<span id="cb69-32"><a href="#cb69-32" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb69-33"><a href="#cb69-33" aria-hidden="true" tabindex="-1"></a>random_p4_local_2 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> local_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span></span>
<span id="cb69-34"><a href="#cb69-34" aria-hidden="true" tabindex="-1"></a>                         ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb69-35"><a href="#cb69-35" aria-hidden="true" tabindex="-1"></a>                         (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> industry_type11),</span>
<span id="cb69-36"><a href="#cb69-36" aria-hidden="true" tabindex="-1"></a>                         <span class="at">data =</span> dta1_11class)</span>
<span id="cb69-37"><a href="#cb69-37" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb69-38"><a href="#cb69-38" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"=== 行业随机效应模型创建完成 ===</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>=== 行业随机效应模型创建完成 ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb71"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb71-1"><a href="#cb71-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"已创建7个包含行业随机效应的模型</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>已创建7个包含行业随机效应的模型</code></pre>
</div>
</div>
</section>
<section id="第四步模型性能指标计算" class="level2">
<h2 class="anchored" data-anchor-id="第四步模型性能指标计算">第四步：模型性能指标计算</h2>
<div class="cell">
<div class="sourceCode cell-code" id="cb73"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb73-1"><a href="#cb73-1" aria-hidden="true" tabindex="-1"></a><span class="co"># ===== 计算所有模型的性能指标 =====</span></span>
<span id="cb73-2"><a href="#cb73-2" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(performance)</span>
<span id="cb73-3"><a href="#cb73-3" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(MuMIn)</span>
<span id="cb73-4"><a href="#cb73-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb73-5"><a href="#cb73-5" aria-hidden="true" tabindex="-1"></a><span class="co"># 创建模型列表</span></span>
<span id="cb73-6"><a href="#cb73-6" aria-hidden="true" tabindex="-1"></a>baseline_models <span class="ot">&lt;-</span> <span class="fu">list</span>(</span>
<span id="cb73-7"><a href="#cb73-7" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P3_1_baseline"</span> <span class="ot">=</span> baseline_p3_1,</span>
<span id="cb73-8"><a href="#cb73-8" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P3_2_baseline"</span> <span class="ot">=</span> baseline_p3_2,</span>
<span id="cb73-9"><a href="#cb73-9" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P3_3_baseline"</span> <span class="ot">=</span> baseline_p3_3,</span>
<span id="cb73-10"><a href="#cb73-10" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P4_central_1_baseline"</span> <span class="ot">=</span> baseline_p4_central_1,</span>
<span id="cb73-11"><a href="#cb73-11" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P4_central_2_baseline"</span> <span class="ot">=</span> baseline_p4_central_2,</span>
<span id="cb73-12"><a href="#cb73-12" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P4_local_1_baseline"</span> <span class="ot">=</span> baseline_p4_local_1,</span>
<span id="cb73-13"><a href="#cb73-13" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P4_local_2_baseline"</span> <span class="ot">=</span> baseline_p4_local_2</span>
<span id="cb73-14"><a href="#cb73-14" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb73-15"><a href="#cb73-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb73-16"><a href="#cb73-16" aria-hidden="true" tabindex="-1"></a>fixed_models <span class="ot">&lt;-</span> <span class="fu">list</span>(</span>
<span id="cb73-17"><a href="#cb73-17" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P3_1_fixed"</span> <span class="ot">=</span> fixed_p3_1,</span>
<span id="cb73-18"><a href="#cb73-18" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P3_2_fixed"</span> <span class="ot">=</span> fixed_p3_2,</span>
<span id="cb73-19"><a href="#cb73-19" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P3_3_fixed"</span> <span class="ot">=</span> fixed_p3_3,</span>
<span id="cb73-20"><a href="#cb73-20" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P4_central_1_fixed"</span> <span class="ot">=</span> fixed_p4_central_1,</span>
<span id="cb73-21"><a href="#cb73-21" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P4_central_2_fixed"</span> <span class="ot">=</span> fixed_p4_central_2,</span>
<span id="cb73-22"><a href="#cb73-22" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P4_local_1_fixed"</span> <span class="ot">=</span> fixed_p4_local_1,</span>
<span id="cb73-23"><a href="#cb73-23" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P4_local_2_fixed"</span> <span class="ot">=</span> fixed_p4_local_2</span>
<span id="cb73-24"><a href="#cb73-24" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb73-25"><a href="#cb73-25" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb73-26"><a href="#cb73-26" aria-hidden="true" tabindex="-1"></a>random_models <span class="ot">&lt;-</span> <span class="fu">list</span>(</span>
<span id="cb73-27"><a href="#cb73-27" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P3_1_random"</span> <span class="ot">=</span> random_p3_1,</span>
<span id="cb73-28"><a href="#cb73-28" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P3_2_random"</span> <span class="ot">=</span> random_p3_2,</span>
<span id="cb73-29"><a href="#cb73-29" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P3_3_random"</span> <span class="ot">=</span> random_p3_3,</span>
<span id="cb73-30"><a href="#cb73-30" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P4_central_1_random"</span> <span class="ot">=</span> random_p4_central_1,</span>
<span id="cb73-31"><a href="#cb73-31" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P4_central_2_random"</span> <span class="ot">=</span> random_p4_central_2,</span>
<span id="cb73-32"><a href="#cb73-32" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P4_local_1_random"</span> <span class="ot">=</span> random_p4_local_1,</span>
<span id="cb73-33"><a href="#cb73-33" aria-hidden="true" tabindex="-1"></a>  <span class="st">"P4_local_2_random"</span> <span class="ot">=</span> random_p4_local_2</span>
<span id="cb73-34"><a href="#cb73-34" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb73-35"><a href="#cb73-35" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb73-36"><a href="#cb73-36" aria-hidden="true" tabindex="-1"></a><span class="co"># 计算性能指标的函数</span></span>
<span id="cb73-37"><a href="#cb73-37" aria-hidden="true" tabindex="-1"></a>calculate_model_metrics <span class="ot">&lt;-</span> <span class="cf">function</span>(model_list, model_type) {</span>
<span id="cb73-38"><a href="#cb73-38" aria-hidden="true" tabindex="-1"></a>  results <span class="ot">&lt;-</span> <span class="fu">data.frame</span>()</span>
<span id="cb73-39"><a href="#cb73-39" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb73-40"><a href="#cb73-40" aria-hidden="true" tabindex="-1"></a>  <span class="cf">for</span>(i <span class="cf">in</span> <span class="dv">1</span><span class="sc">:</span><span class="fu">length</span>(model_list)) {</span>
<span id="cb73-41"><a href="#cb73-41" aria-hidden="true" tabindex="-1"></a>    model <span class="ot">&lt;-</span> model_list[[i]]</span>
<span id="cb73-42"><a href="#cb73-42" aria-hidden="true" tabindex="-1"></a>    model_name <span class="ot">&lt;-</span> <span class="fu">names</span>(model_list)[i]</span>
<span id="cb73-43"><a href="#cb73-43" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb73-44"><a href="#cb73-44" aria-hidden="true" tabindex="-1"></a>    <span class="co"># 基本信息</span></span>
<span id="cb73-45"><a href="#cb73-45" aria-hidden="true" tabindex="-1"></a>    aic_val <span class="ot">&lt;-</span> <span class="fu">AIC</span>(model)</span>
<span id="cb73-46"><a href="#cb73-46" aria-hidden="true" tabindex="-1"></a>    bic_val <span class="ot">&lt;-</span> <span class="fu">BIC</span>(model)</span>
<span id="cb73-47"><a href="#cb73-47" aria-hidden="true" tabindex="-1"></a>    loglik_val <span class="ot">&lt;-</span> <span class="fu">as.numeric</span>(<span class="fu">logLik</span>(model))</span>
<span id="cb73-48"><a href="#cb73-48" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb73-49"><a href="#cb73-49" aria-hidden="true" tabindex="-1"></a>    <span class="co"># R-squared</span></span>
<span id="cb73-50"><a href="#cb73-50" aria-hidden="true" tabindex="-1"></a>    r2_vals <span class="ot">&lt;-</span> <span class="fu">r.squaredGLMM</span>(model)</span>
<span id="cb73-51"><a href="#cb73-51" aria-hidden="true" tabindex="-1"></a>    r2_marginal <span class="ot">&lt;-</span> r2_vals[<span class="dv">1</span>]</span>
<span id="cb73-52"><a href="#cb73-52" aria-hidden="true" tabindex="-1"></a>    r2_conditional <span class="ot">&lt;-</span> r2_vals[<span class="dv">2</span>]</span>
<span id="cb73-53"><a href="#cb73-53" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb73-54"><a href="#cb73-54" aria-hidden="true" tabindex="-1"></a>    <span class="co"># ICC</span></span>
<span id="cb73-55"><a href="#cb73-55" aria-hidden="true" tabindex="-1"></a>    icc_val <span class="ot">&lt;-</span> performance<span class="sc">::</span><span class="fu">icc</span>(model)<span class="sc">$</span>ICC_adjusted</span>
<span id="cb73-56"><a href="#cb73-56" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb73-57"><a href="#cb73-57" aria-hidden="true" tabindex="-1"></a>    <span class="co"># 自由度和观测数</span></span>
<span id="cb73-58"><a href="#cb73-58" aria-hidden="true" tabindex="-1"></a>    df_val <span class="ot">&lt;-</span> <span class="fu">attr</span>(<span class="fu">logLik</span>(model), <span class="st">"df"</span>)</span>
<span id="cb73-59"><a href="#cb73-59" aria-hidden="true" tabindex="-1"></a>    nobs_val <span class="ot">&lt;-</span> <span class="fu">nobs</span>(model)</span>
<span id="cb73-60"><a href="#cb73-60" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb73-61"><a href="#cb73-61" aria-hidden="true" tabindex="-1"></a>    <span class="co"># 组合结果</span></span>
<span id="cb73-62"><a href="#cb73-62" aria-hidden="true" tabindex="-1"></a>    row_result <span class="ot">&lt;-</span> <span class="fu">data.frame</span>(</span>
<span id="cb73-63"><a href="#cb73-63" aria-hidden="true" tabindex="-1"></a>      <span class="at">Model =</span> model_name,</span>
<span id="cb73-64"><a href="#cb73-64" aria-hidden="true" tabindex="-1"></a>      <span class="at">Type =</span> model_type,</span>
<span id="cb73-65"><a href="#cb73-65" aria-hidden="true" tabindex="-1"></a>      <span class="at">AIC =</span> aic_val,</span>
<span id="cb73-66"><a href="#cb73-66" aria-hidden="true" tabindex="-1"></a>      <span class="at">BIC =</span> bic_val,</span>
<span id="cb73-67"><a href="#cb73-67" aria-hidden="true" tabindex="-1"></a>      <span class="at">LogLik =</span> loglik_val,</span>
<span id="cb73-68"><a href="#cb73-68" aria-hidden="true" tabindex="-1"></a>      <span class="at">R2_marginal =</span> r2_marginal,</span>
<span id="cb73-69"><a href="#cb73-69" aria-hidden="true" tabindex="-1"></a>      <span class="at">R2_conditional =</span> r2_conditional,</span>
<span id="cb73-70"><a href="#cb73-70" aria-hidden="true" tabindex="-1"></a>      <span class="at">ICC =</span> icc_val,</span>
<span id="cb73-71"><a href="#cb73-71" aria-hidden="true" tabindex="-1"></a>      <span class="at">DF =</span> df_val,</span>
<span id="cb73-72"><a href="#cb73-72" aria-hidden="true" tabindex="-1"></a>      <span class="at">N_obs =</span> nobs_val,</span>
<span id="cb73-73"><a href="#cb73-73" aria-hidden="true" tabindex="-1"></a>      <span class="at">stringsAsFactors =</span> <span class="cn">FALSE</span></span>
<span id="cb73-74"><a href="#cb73-74" aria-hidden="true" tabindex="-1"></a>    )</span>
<span id="cb73-75"><a href="#cb73-75" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb73-76"><a href="#cb73-76" aria-hidden="true" tabindex="-1"></a>    results <span class="ot">&lt;-</span> <span class="fu">rbind</span>(results, row_result)</span>
<span id="cb73-77"><a href="#cb73-77" aria-hidden="true" tabindex="-1"></a>  }</span>
<span id="cb73-78"><a href="#cb73-78" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb73-79"><a href="#cb73-79" aria-hidden="true" tabindex="-1"></a>  <span class="fu">return</span>(results)</span>
<span id="cb73-80"><a href="#cb73-80" aria-hidden="true" tabindex="-1"></a>}</span>
<span id="cb73-81"><a href="#cb73-81" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb73-82"><a href="#cb73-82" aria-hidden="true" tabindex="-1"></a><span class="co"># 计算所有模型的指标</span></span>
<span id="cb73-83"><a href="#cb73-83" aria-hidden="true" tabindex="-1"></a>baseline_metrics <span class="ot">&lt;-</span> <span class="fu">calculate_model_metrics</span>(baseline_models, <span class="st">"Baseline"</span>)</span>
<span id="cb73-84"><a href="#cb73-84" aria-hidden="true" tabindex="-1"></a>fixed_metrics <span class="ot">&lt;-</span> <span class="fu">calculate_model_metrics</span>(fixed_models, <span class="st">"Fixed_Industry"</span>)</span>
<span id="cb73-85"><a href="#cb73-85" aria-hidden="true" tabindex="-1"></a>random_metrics <span class="ot">&lt;-</span> <span class="fu">calculate_model_metrics</span>(random_models, <span class="st">"Random_Industry"</span>)</span>
<span id="cb73-86"><a href="#cb73-86" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb73-87"><a href="#cb73-87" aria-hidden="true" tabindex="-1"></a><span class="co"># 合并所有结果</span></span>
<span id="cb73-88"><a href="#cb73-88" aria-hidden="true" tabindex="-1"></a>all_metrics <span class="ot">&lt;-</span> <span class="fu">rbind</span>(baseline_metrics, fixed_metrics, random_metrics)</span>
<span id="cb73-89"><a href="#cb73-89" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb73-90"><a href="#cb73-90" aria-hidden="true" tabindex="-1"></a><span class="co"># 显示结果</span></span>
<span id="cb73-91"><a href="#cb73-91" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"=== 模型性能指标计算完成 ===</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>=== 模型性能指标计算完成 ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb75"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb75-1"><a href="#cb75-1" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(all_metrics)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>                   Model            Type      AIC      BIC    LogLik
1          P3_1_baseline        Baseline 83024.31 83097.15 -41502.15
2          P3_2_baseline        Baseline 82407.56 82473.13 -41194.78
3          P3_3_baseline        Baseline 82345.23 82432.64 -41160.61
4  P4_central_1_baseline        Baseline 83041.52 83114.37 -41510.76
5  P4_central_2_baseline        Baseline 82401.39 82481.53 -41189.70
6    P4_local_1_baseline        Baseline 83029.62 83102.47 -41504.81
7    P4_local_2_baseline        Baseline 82404.06 82484.20 -41191.03
8             P3_1_fixed  Fixed_Industry 82416.62 82562.31 -41188.31
9             P3_2_fixed  Fixed_Industry 81876.23 82014.64 -40919.11
10            P3_3_fixed  Fixed_Industry 81809.46 81969.72 -40882.73
11    P4_central_1_fixed  Fixed_Industry 82415.60 82561.29 -41187.80
12    P4_central_2_fixed  Fixed_Industry 81872.90 82025.89 -40915.45
13      P4_local_1_fixed  Fixed_Industry 82418.62 82564.31 -41189.31
14      P4_local_2_fixed  Fixed_Industry 81884.08 82037.07 -40921.04
15           P3_1_random Random_Industry 82456.09 82536.22 -41217.05
16           P3_2_random Random_Industry 81913.35 81986.20 -40946.67
17           P3_3_random Random_Industry 81846.82 81941.52 -40910.41
18   P4_central_1_random Random_Industry 82455.23 82535.36 -41216.62
19   P4_central_2_random Random_Industry 81909.89 81997.31 -40942.94
20     P4_local_1_random Random_Industry 82458.17 82538.30 -41218.08
21     P4_local_2_random Random_Industry 81921.02 82008.45 -40948.51
   R2_marginal R2_conditional        ICC DF N_obs
1    0.1290546      0.2167671 0.10070948 10 10771
2    0.1787492      0.2584431 0.09703973  9 10777
3    0.1787057      0.2595655 0.09845407 12 10771
4    0.1269716      0.2147829 0.10058236 10 10771
5    0.1793868      0.2599560 0.09818171 11 10777
6    0.1287120      0.2162090 0.10042252 10 10771
7    0.1800022      0.2601754 0.09777245 11 10777
8    0.1796550      0.2768343 0.11846154 20 10771
9    0.2204469      0.3067296 0.11068232 19 10777
10   0.2205166      0.3090521 0.11358227 22 10771
11   0.1794112      0.2759723 0.11767304 20 10771
12   0.2209070      0.3080218 0.11181567 21 10777
13   0.1794900      0.2765293 0.11826709 20 10771
14   0.2206665      0.3074628 0.11137242 21 10777
15   0.1408388      0.3138753 0.20140176 11 10771
16   0.1797171      0.3287740 0.18171393 10 10777
17   0.1790736      0.3318013 0.18604311 13 10771
18   0.1408891      0.3144909 0.20207148 11 10771
19   0.1801719      0.3292528 0.18184408 12 10777
20   0.1407607      0.3141560 0.20180088 11 10771
21   0.1798952      0.3284251 0.18111088 12 10777</code></pre>
</div>
</div>
</section>
<section id="第五步似然比检验" class="level2">
<h2 class="anchored" data-anchor-id="第五步似然比检验">第五步：似然比检验</h2>
<div class="cell">
<div class="sourceCode cell-code" id="cb77"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb77-1"><a href="#cb77-1" aria-hidden="true" tabindex="-1"></a><span class="co"># ===== 似然比检验：比较嵌套模型 =====</span></span>
<span id="cb77-2"><a href="#cb77-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb77-3"><a href="#cb77-3" aria-hidden="true" tabindex="-1"></a><span class="co"># 创建比较函数</span></span>
<span id="cb77-4"><a href="#cb77-4" aria-hidden="true" tabindex="-1"></a>perform_lr_test <span class="ot">&lt;-</span> <span class="cf">function</span>(baseline_model, comparison_model, comparison_name) {</span>
<span id="cb77-5"><a href="#cb77-5" aria-hidden="true" tabindex="-1"></a>  lr_test <span class="ot">&lt;-</span> <span class="fu">anova</span>(baseline_model, comparison_model)</span>
<span id="cb77-6"><a href="#cb77-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb77-7"><a href="#cb77-7" aria-hidden="true" tabindex="-1"></a>  result <span class="ot">&lt;-</span> <span class="fu">data.frame</span>(</span>
<span id="cb77-8"><a href="#cb77-8" aria-hidden="true" tabindex="-1"></a>    <span class="at">Comparison =</span> comparison_name,</span>
<span id="cb77-9"><a href="#cb77-9" aria-hidden="true" tabindex="-1"></a>    <span class="at">Chi_square =</span> lr_test<span class="sc">$</span>Chisq[<span class="dv">2</span>],</span>
<span id="cb77-10"><a href="#cb77-10" aria-hidden="true" tabindex="-1"></a>    <span class="at">DF_diff =</span> lr_test<span class="sc">$</span>Df[<span class="dv">2</span>],</span>
<span id="cb77-11"><a href="#cb77-11" aria-hidden="true" tabindex="-1"></a>    <span class="at">P_value =</span> lr_test<span class="sc">$</span><span class="st">`</span><span class="at">Pr(&gt;Chisq)</span><span class="st">`</span>[<span class="dv">2</span>],</span>
<span id="cb77-12"><a href="#cb77-12" aria-hidden="true" tabindex="-1"></a>    <span class="at">Significant =</span> lr_test<span class="sc">$</span><span class="st">`</span><span class="at">Pr(&gt;Chisq)</span><span class="st">`</span>[<span class="dv">2</span>] <span class="sc">&lt;</span> <span class="fl">0.05</span>,</span>
<span id="cb77-13"><a href="#cb77-13" aria-hidden="true" tabindex="-1"></a>    <span class="at">stringsAsFactors =</span> <span class="cn">FALSE</span></span>
<span id="cb77-14"><a href="#cb77-14" aria-hidden="true" tabindex="-1"></a>  )</span>
<span id="cb77-15"><a href="#cb77-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb77-16"><a href="#cb77-16" aria-hidden="true" tabindex="-1"></a>  <span class="fu">return</span>(result)</span>
<span id="cb77-17"><a href="#cb77-17" aria-hidden="true" tabindex="-1"></a>}</span>
<span id="cb77-18"><a href="#cb77-18" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb77-19"><a href="#cb77-19" aria-hidden="true" tabindex="-1"></a><span class="co"># 进行所有的似然比检验</span></span>
<span id="cb77-20"><a href="#cb77-20" aria-hidden="true" tabindex="-1"></a>lr_results <span class="ot">&lt;-</span> <span class="fu">data.frame</span>()</span>
<span id="cb77-21"><a href="#cb77-21" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb77-22"><a href="#cb77-22" aria-hidden="true" tabindex="-1"></a><span class="co"># P3系列检验</span></span>
<span id="cb77-23"><a href="#cb77-23" aria-hidden="true" tabindex="-1"></a>lr_results <span class="ot">&lt;-</span> <span class="fu">rbind</span>(lr_results, <span class="fu">perform_lr_test</span>(baseline_p3_1, fixed_p3_1, <span class="st">"P3_1: Baseline vs Fixed"</span>))</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>refitting model(s) with ML (instead of REML)</code></pre>
</div>
<div class="sourceCode cell-code" id="cb79"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb79-1"><a href="#cb79-1" aria-hidden="true" tabindex="-1"></a>lr_results <span class="ot">&lt;-</span> <span class="fu">rbind</span>(lr_results, <span class="fu">perform_lr_test</span>(baseline_p3_1, random_p3_1, <span class="st">"P3_1: Baseline vs Random"</span>))</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>refitting model(s) with ML (instead of REML)</code></pre>
</div>
<div class="sourceCode cell-code" id="cb81"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb81-1"><a href="#cb81-1" aria-hidden="true" tabindex="-1"></a>lr_results <span class="ot">&lt;-</span> <span class="fu">rbind</span>(lr_results, <span class="fu">perform_lr_test</span>(baseline_p3_2, fixed_p3_2, <span class="st">"P3_2: Baseline vs Fixed"</span>))</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>refitting model(s) with ML (instead of REML)</code></pre>
</div>
<div class="sourceCode cell-code" id="cb83"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb83-1"><a href="#cb83-1" aria-hidden="true" tabindex="-1"></a>lr_results <span class="ot">&lt;-</span> <span class="fu">rbind</span>(lr_results, <span class="fu">perform_lr_test</span>(baseline_p3_2, random_p3_2, <span class="st">"P3_2: Baseline vs Random"</span>))</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>refitting model(s) with ML (instead of REML)</code></pre>
</div>
<div class="sourceCode cell-code" id="cb85"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb85-1"><a href="#cb85-1" aria-hidden="true" tabindex="-1"></a>lr_results <span class="ot">&lt;-</span> <span class="fu">rbind</span>(lr_results, <span class="fu">perform_lr_test</span>(baseline_p3_3, fixed_p3_3, <span class="st">"P3_3: Baseline vs Fixed"</span>))</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>refitting model(s) with ML (instead of REML)</code></pre>
</div>
<div class="sourceCode cell-code" id="cb87"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb87-1"><a href="#cb87-1" aria-hidden="true" tabindex="-1"></a>lr_results <span class="ot">&lt;-</span> <span class="fu">rbind</span>(lr_results, <span class="fu">perform_lr_test</span>(baseline_p3_3, random_p3_3, <span class="st">"P3_3: Baseline vs Random"</span>))</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>refitting model(s) with ML (instead of REML)</code></pre>
</div>
<div class="sourceCode cell-code" id="cb89"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb89-1"><a href="#cb89-1" aria-hidden="true" tabindex="-1"></a><span class="co"># P4中央系列检验</span></span>
<span id="cb89-2"><a href="#cb89-2" aria-hidden="true" tabindex="-1"></a>lr_results <span class="ot">&lt;-</span> <span class="fu">rbind</span>(lr_results, <span class="fu">perform_lr_test</span>(baseline_p4_central_1, fixed_p4_central_1, <span class="st">"P4_Central_1: Baseline vs Fixed"</span>))</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>refitting model(s) with ML (instead of REML)</code></pre>
</div>
<div class="sourceCode cell-code" id="cb91"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb91-1"><a href="#cb91-1" aria-hidden="true" tabindex="-1"></a>lr_results <span class="ot">&lt;-</span> <span class="fu">rbind</span>(lr_results, <span class="fu">perform_lr_test</span>(baseline_p4_central_1, random_p4_central_1, <span class="st">"P4_Central_1: Baseline vs Random"</span>))</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>refitting model(s) with ML (instead of REML)</code></pre>
</div>
<div class="sourceCode cell-code" id="cb93"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb93-1"><a href="#cb93-1" aria-hidden="true" tabindex="-1"></a>lr_results <span class="ot">&lt;-</span> <span class="fu">rbind</span>(lr_results, <span class="fu">perform_lr_test</span>(baseline_p4_central_2, fixed_p4_central_2, <span class="st">"P4_Central_2: Baseline vs Fixed"</span>))</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>refitting model(s) with ML (instead of REML)</code></pre>
</div>
<div class="sourceCode cell-code" id="cb95"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb95-1"><a href="#cb95-1" aria-hidden="true" tabindex="-1"></a>lr_results <span class="ot">&lt;-</span> <span class="fu">rbind</span>(lr_results, <span class="fu">perform_lr_test</span>(baseline_p4_central_2, random_p4_central_2, <span class="st">"P4_Central_2: Baseline vs Random"</span>))</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>refitting model(s) with ML (instead of REML)</code></pre>
</div>
<div class="sourceCode cell-code" id="cb97"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb97-1"><a href="#cb97-1" aria-hidden="true" tabindex="-1"></a><span class="co"># P4地方系列检验</span></span>
<span id="cb97-2"><a href="#cb97-2" aria-hidden="true" tabindex="-1"></a>lr_results <span class="ot">&lt;-</span> <span class="fu">rbind</span>(lr_results, <span class="fu">perform_lr_test</span>(baseline_p4_local_1, fixed_p4_local_1, <span class="st">"P4_Local_1: Baseline vs Fixed"</span>))</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>refitting model(s) with ML (instead of REML)</code></pre>
</div>
<div class="sourceCode cell-code" id="cb99"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb99-1"><a href="#cb99-1" aria-hidden="true" tabindex="-1"></a>lr_results <span class="ot">&lt;-</span> <span class="fu">rbind</span>(lr_results, <span class="fu">perform_lr_test</span>(baseline_p4_local_1, random_p4_local_1, <span class="st">"P4_Local_1: Baseline vs Random"</span>))</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>refitting model(s) with ML (instead of REML)</code></pre>
</div>
<div class="sourceCode cell-code" id="cb101"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb101-1"><a href="#cb101-1" aria-hidden="true" tabindex="-1"></a>lr_results <span class="ot">&lt;-</span> <span class="fu">rbind</span>(lr_results, <span class="fu">perform_lr_test</span>(baseline_p4_local_2, fixed_p4_local_2, <span class="st">"P4_Local_2: Baseline vs Fixed"</span>))</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>refitting model(s) with ML (instead of REML)</code></pre>
</div>
<div class="sourceCode cell-code" id="cb103"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb103-1"><a href="#cb103-1" aria-hidden="true" tabindex="-1"></a>lr_results <span class="ot">&lt;-</span> <span class="fu">rbind</span>(lr_results, <span class="fu">perform_lr_test</span>(baseline_p4_local_2, random_p4_local_2, <span class="st">"P4_Local_2: Baseline vs Random"</span>))</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>refitting model(s) with ML (instead of REML)</code></pre>
</div>
<div class="sourceCode cell-code" id="cb105"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb105-1"><a href="#cb105-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 显示似然比检验结果</span></span>
<span id="cb105-2"><a href="#cb105-2" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"=== 似然比检验结果 ===</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>=== 似然比检验结果 ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb107"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb107-1"><a href="#cb107-1" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(lr_results)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>                         Comparison Chi_square DF_diff       P_value
1           P3_1: Baseline vs Fixed   623.5988      10 1.542137e-127
2          P3_1: Baseline vs Random   568.0100       1 1.521260e-125
3           P3_2: Baseline vs Fixed   547.8807      10 2.546741e-111
4          P3_2: Baseline vs Random   494.1950       1 1.741931e-109
5           P3_3: Baseline vs Fixed   552.3436      10 2.824465e-112
6          P3_3: Baseline vs Random   498.3794       1 2.140852e-110
7   P4_Central_1: Baseline vs Fixed   641.8602      10 1.873682e-131
8  P4_Central_1: Baseline vs Random   586.0719       1 1.792005e-129
9   P4_Central_2: Baseline vs Fixed   545.0915      10 1.006541e-110
10 P4_Central_2: Baseline vs Random   491.5567       1 6.532623e-109
11    P4_Local_1: Baseline vs Fixed   626.9231      10 2.988619e-128
12   P4_Local_1: Baseline vs Random   571.2540       1 2.995958e-126
13    P4_Local_2: Baseline vs Fixed   536.5582      10 6.737498e-109
14   P4_Local_2: Baseline vs Random   483.0750       1 4.577574e-107
   Significant
1         TRUE
2         TRUE
3         TRUE
4         TRUE
5         TRUE
6         TRUE
7         TRUE
8         TRUE
9         TRUE
10        TRUE
11        TRUE
12        TRUE
13        TRUE
14        TRUE</code></pre>
</div>
<div class="sourceCode cell-code" id="cb109"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb109-1"><a href="#cb109-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 统计显著性结果</span></span>
<span id="cb109-2"><a href="#cb109-2" aria-hidden="true" tabindex="-1"></a>significant_improvements <span class="ot">&lt;-</span> <span class="fu">sum</span>(lr_results<span class="sc">$</span>Significant)</span>
<span id="cb109-3"><a href="#cb109-3" aria-hidden="true" tabindex="-1"></a>total_comparisons <span class="ot">&lt;-</span> <span class="fu">nrow</span>(lr_results)</span>
<span id="cb109-4"><a href="#cb109-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb109-5"><a href="#cb109-5" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"</span><span class="sc">\n</span><span class="st">=== 似然比检验总结 ===</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
=== 似然比检验总结 ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb111"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb111-1"><a href="#cb111-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"总比较次数:"</span>, total_comparisons, <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>总比较次数: 14 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb113"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb113-1"><a href="#cb113-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"显著改善的模型数:"</span>, significant_improvements, <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>显著改善的模型数: 14 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb115"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb115-1"><a href="#cb115-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"显著改善比例:"</span>, <span class="fu">round</span>(significant_improvements<span class="sc">/</span>total_comparisons<span class="sc">*</span><span class="dv">100</span>, <span class="dv">2</span>), <span class="st">"%</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>显著改善比例: 100 %</code></pre>
</div>
</div>
</section>
<section id="第六步创建模型比较汇总表格" class="level2">
<h2 class="anchored" data-anchor-id="第六步创建模型比较汇总表格">第六步：创建模型比较汇总表格</h2>
<div class="cell">
<div class="sourceCode cell-code" id="cb117"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb117-1"><a href="#cb117-1" aria-hidden="true" tabindex="-1"></a><span class="co"># ===== 创建详细的模型比较表格 =====</span></span>
<span id="cb117-2"><a href="#cb117-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb117-3"><a href="#cb117-3" aria-hidden="true" tabindex="-1"></a><span class="co"># 重新整理数据以便比较</span></span>
<span id="cb117-4"><a href="#cb117-4" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(dplyr)</span>
<span id="cb117-5"><a href="#cb117-5" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(kableExtra)</span>
<span id="cb117-6"><a href="#cb117-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb117-7"><a href="#cb117-7" aria-hidden="true" tabindex="-1"></a><span class="co"># 为每个模型组创建比较表</span></span>
<span id="cb117-8"><a href="#cb117-8" aria-hidden="true" tabindex="-1"></a>create_comparison_table <span class="ot">&lt;-</span> <span class="cf">function</span>(baseline_name, fixed_name, random_name) {</span>
<span id="cb117-9"><a href="#cb117-9" aria-hidden="true" tabindex="-1"></a>  baseline_row <span class="ot">&lt;-</span> all_metrics[all_metrics<span class="sc">$</span>Model <span class="sc">==</span> baseline_name, ]</span>
<span id="cb117-10"><a href="#cb117-10" aria-hidden="true" tabindex="-1"></a>  fixed_row <span class="ot">&lt;-</span> all_metrics[all_metrics<span class="sc">$</span>Model <span class="sc">==</span> fixed_name, ]</span>
<span id="cb117-11"><a href="#cb117-11" aria-hidden="true" tabindex="-1"></a>  random_row <span class="ot">&lt;-</span> all_metrics[all_metrics<span class="sc">$</span>Model <span class="sc">==</span> random_name, ]</span>
<span id="cb117-12"><a href="#cb117-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb117-13"><a href="#cb117-13" aria-hidden="true" tabindex="-1"></a>  comparison_df <span class="ot">&lt;-</span> <span class="fu">rbind</span>(baseline_row, fixed_row, random_row)</span>
<span id="cb117-14"><a href="#cb117-14" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb117-15"><a href="#cb117-15" aria-hidden="true" tabindex="-1"></a>  <span class="co"># 计算相对于基准模型的变化</span></span>
<span id="cb117-16"><a href="#cb117-16" aria-hidden="true" tabindex="-1"></a>  comparison_df<span class="sc">$</span>AIC_diff <span class="ot">&lt;-</span> comparison_df<span class="sc">$</span>AIC <span class="sc">-</span> baseline_row<span class="sc">$</span>AIC</span>
<span id="cb117-17"><a href="#cb117-17" aria-hidden="true" tabindex="-1"></a>  comparison_df<span class="sc">$</span>BIC_diff <span class="ot">&lt;-</span> comparison_df<span class="sc">$</span>BIC <span class="sc">-</span> baseline_row<span class="sc">$</span>BIC</span>
<span id="cb117-18"><a href="#cb117-18" aria-hidden="true" tabindex="-1"></a>  comparison_df<span class="sc">$</span>LogLik_diff <span class="ot">&lt;-</span> comparison_df<span class="sc">$</span>LogLik <span class="sc">-</span> baseline_row<span class="sc">$</span>LogLik</span>
<span id="cb117-19"><a href="#cb117-19" aria-hidden="true" tabindex="-1"></a>  comparison_df<span class="sc">$</span>R2_marginal_diff <span class="ot">&lt;-</span> comparison_df<span class="sc">$</span>R2_marginal <span class="sc">-</span> baseline_row<span class="sc">$</span>R2_marginal</span>
<span id="cb117-20"><a href="#cb117-20" aria-hidden="true" tabindex="-1"></a>  comparison_df<span class="sc">$</span>R2_conditional_diff <span class="ot">&lt;-</span> comparison_df<span class="sc">$</span>R2_conditional <span class="sc">-</span> baseline_row<span class="sc">$</span>R2_conditional</span>
<span id="cb117-21"><a href="#cb117-21" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb117-22"><a href="#cb117-22" aria-hidden="true" tabindex="-1"></a>  <span class="fu">return</span>(comparison_df)</span>
<span id="cb117-23"><a href="#cb117-23" aria-hidden="true" tabindex="-1"></a>}</span>
<span id="cb117-24"><a href="#cb117-24" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb117-25"><a href="#cb117-25" aria-hidden="true" tabindex="-1"></a><span class="co"># P3系列比较</span></span>
<span id="cb117-26"><a href="#cb117-26" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"=== P3系列模型比较 ===</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>=== P3系列模型比较 ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb119"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb119-1"><a href="#cb119-1" aria-hidden="true" tabindex="-1"></a>p3_1_comparison <span class="ot">&lt;-</span> <span class="fu">create_comparison_table</span>(<span class="st">"P3_1_baseline"</span>, <span class="st">"P3_1_fixed"</span>, <span class="st">"P3_1_random"</span>)</span>
<span id="cb119-2"><a href="#cb119-2" aria-hidden="true" tabindex="-1"></a>p3_2_comparison <span class="ot">&lt;-</span> <span class="fu">create_comparison_table</span>(<span class="st">"P3_2_baseline"</span>, <span class="st">"P3_2_fixed"</span>, <span class="st">"P3_2_random"</span>)</span>
<span id="cb119-3"><a href="#cb119-3" aria-hidden="true" tabindex="-1"></a>p3_3_comparison <span class="ot">&lt;-</span> <span class="fu">create_comparison_table</span>(<span class="st">"P3_3_baseline"</span>, <span class="st">"P3_3_fixed"</span>, <span class="st">"P3_3_random"</span>)</span>
<span id="cb119-4"><a href="#cb119-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb119-5"><a href="#cb119-5" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(<span class="st">"P3_1 模型比较:"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>[1] "P3_1 模型比较:"</code></pre>
</div>
<div class="sourceCode cell-code" id="cb121"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb121-1"><a href="#cb121-1" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(p3_1_comparison[, <span class="fu">c</span>(<span class="st">"Type"</span>, <span class="st">"AIC"</span>, <span class="st">"BIC"</span>, <span class="st">"LogLik"</span>, <span class="st">"R2_marginal"</span>, <span class="st">"R2_conditional"</span>, <span class="st">"ICC"</span>)])</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>              Type      AIC      BIC    LogLik R2_marginal R2_conditional
1         Baseline 83024.31 83097.15 -41502.15   0.1290546      0.2167671
8   Fixed_Industry 82416.62 82562.31 -41188.31   0.1796550      0.2768343
15 Random_Industry 82456.09 82536.22 -41217.05   0.1408388      0.3138753
         ICC
1  0.1007095
8  0.1184615
15 0.2014018</code></pre>
</div>
<div class="sourceCode cell-code" id="cb123"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb123-1"><a href="#cb123-1" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(<span class="st">"P3_2 模型比较:"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>[1] "P3_2 模型比较:"</code></pre>
</div>
<div class="sourceCode cell-code" id="cb125"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb125-1"><a href="#cb125-1" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(p3_2_comparison[, <span class="fu">c</span>(<span class="st">"Type"</span>, <span class="st">"AIC"</span>, <span class="st">"BIC"</span>, <span class="st">"LogLik"</span>, <span class="st">"R2_marginal"</span>, <span class="st">"R2_conditional"</span>, <span class="st">"ICC"</span>)])</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>              Type      AIC      BIC    LogLik R2_marginal R2_conditional
2         Baseline 82407.56 82473.13 -41194.78   0.1787492      0.2584431
9   Fixed_Industry 81876.23 82014.64 -40919.11   0.2204469      0.3067296
16 Random_Industry 81913.35 81986.20 -40946.67   0.1797171      0.3287740
          ICC
2  0.09703973
9  0.11068232
16 0.18171393</code></pre>
</div>
<div class="sourceCode cell-code" id="cb127"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb127-1"><a href="#cb127-1" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(<span class="st">"P3_3 模型比较:"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>[1] "P3_3 模型比较:"</code></pre>
</div>
<div class="sourceCode cell-code" id="cb129"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb129-1"><a href="#cb129-1" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(p3_3_comparison[, <span class="fu">c</span>(<span class="st">"Type"</span>, <span class="st">"AIC"</span>, <span class="st">"BIC"</span>, <span class="st">"LogLik"</span>, <span class="st">"R2_marginal"</span>, <span class="st">"R2_conditional"</span>, <span class="st">"ICC"</span>)])</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>              Type      AIC      BIC    LogLik R2_marginal R2_conditional
3         Baseline 82345.23 82432.64 -41160.61   0.1787057      0.2595655
10  Fixed_Industry 81809.46 81969.72 -40882.73   0.2205166      0.3090521
17 Random_Industry 81846.82 81941.52 -40910.41   0.1790736      0.3318013
          ICC
3  0.09845407
10 0.11358227
17 0.18604311</code></pre>
</div>
<div class="sourceCode cell-code" id="cb131"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb131-1"><a href="#cb131-1" aria-hidden="true" tabindex="-1"></a><span class="co"># P4系列比较</span></span>
<span id="cb131-2"><a href="#cb131-2" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"</span><span class="sc">\n</span><span class="st">=== P4系列模型比较 ===</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
=== P4系列模型比较 ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb133"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb133-1"><a href="#cb133-1" aria-hidden="true" tabindex="-1"></a>p4_central_1_comparison <span class="ot">&lt;-</span> <span class="fu">create_comparison_table</span>(<span class="st">"P4_central_1_baseline"</span>, <span class="st">"P4_central_1_fixed"</span>, <span class="st">"P4_central_1_random"</span>)</span>
<span id="cb133-2"><a href="#cb133-2" aria-hidden="true" tabindex="-1"></a>p4_central_2_comparison <span class="ot">&lt;-</span> <span class="fu">create_comparison_table</span>(<span class="st">"P4_central_2_baseline"</span>, <span class="st">"P4_central_2_fixed"</span>, <span class="st">"P4_central_2_random"</span>)</span>
<span id="cb133-3"><a href="#cb133-3" aria-hidden="true" tabindex="-1"></a>p4_local_1_comparison <span class="ot">&lt;-</span> <span class="fu">create_comparison_table</span>(<span class="st">"P4_local_1_baseline"</span>, <span class="st">"P4_local_1_fixed"</span>, <span class="st">"P4_local_1_random"</span>)</span>
<span id="cb133-4"><a href="#cb133-4" aria-hidden="true" tabindex="-1"></a>p4_local_2_comparison <span class="ot">&lt;-</span> <span class="fu">create_comparison_table</span>(<span class="st">"P4_local_2_baseline"</span>, <span class="st">"P4_local_2_fixed"</span>, <span class="st">"P4_local_2_random"</span>)</span>
<span id="cb133-5"><a href="#cb133-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb133-6"><a href="#cb133-6" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(<span class="st">"P4_Central_1 模型比较:"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>[1] "P4_Central_1 模型比较:"</code></pre>
</div>
<div class="sourceCode cell-code" id="cb135"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb135-1"><a href="#cb135-1" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(p4_central_1_comparison[, <span class="fu">c</span>(<span class="st">"Type"</span>, <span class="st">"AIC"</span>, <span class="st">"BIC"</span>, <span class="st">"LogLik"</span>, <span class="st">"R2_marginal"</span>, <span class="st">"R2_conditional"</span>, <span class="st">"ICC"</span>)])</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>              Type      AIC      BIC    LogLik R2_marginal R2_conditional
4         Baseline 83041.52 83114.37 -41510.76   0.1269716      0.2147829
11  Fixed_Industry 82415.60 82561.29 -41187.80   0.1794112      0.2759723
18 Random_Industry 82455.23 82535.36 -41216.62   0.1408891      0.3144909
         ICC
4  0.1005824
11 0.1176730
18 0.2020715</code></pre>
</div>
<div class="sourceCode cell-code" id="cb137"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb137-1"><a href="#cb137-1" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(<span class="st">"P4_Central_2 模型比较:"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>[1] "P4_Central_2 模型比较:"</code></pre>
</div>
<div class="sourceCode cell-code" id="cb139"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb139-1"><a href="#cb139-1" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(p4_central_2_comparison[, <span class="fu">c</span>(<span class="st">"Type"</span>, <span class="st">"AIC"</span>, <span class="st">"BIC"</span>, <span class="st">"LogLik"</span>, <span class="st">"R2_marginal"</span>, <span class="st">"R2_conditional"</span>, <span class="st">"ICC"</span>)])</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>              Type      AIC      BIC    LogLik R2_marginal R2_conditional
5         Baseline 82401.39 82481.53 -41189.70   0.1793868      0.2599560
12  Fixed_Industry 81872.90 82025.89 -40915.45   0.2209070      0.3080218
19 Random_Industry 81909.89 81997.31 -40942.94   0.1801719      0.3292528
          ICC
5  0.09818171
12 0.11181567
19 0.18184408</code></pre>
</div>
<div class="sourceCode cell-code" id="cb141"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb141-1"><a href="#cb141-1" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(<span class="st">"P4_Local_1 模型比较:"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>[1] "P4_Local_1 模型比较:"</code></pre>
</div>
<div class="sourceCode cell-code" id="cb143"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb143-1"><a href="#cb143-1" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(p4_local_1_comparison[, <span class="fu">c</span>(<span class="st">"Type"</span>, <span class="st">"AIC"</span>, <span class="st">"BIC"</span>, <span class="st">"LogLik"</span>, <span class="st">"R2_marginal"</span>, <span class="st">"R2_conditional"</span>, <span class="st">"ICC"</span>)])</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>              Type      AIC      BIC    LogLik R2_marginal R2_conditional
6         Baseline 83029.62 83102.47 -41504.81   0.1287120      0.2162090
13  Fixed_Industry 82418.62 82564.31 -41189.31   0.1794900      0.2765293
20 Random_Industry 82458.17 82538.30 -41218.08   0.1407607      0.3141560
         ICC
6  0.1004225
13 0.1182671
20 0.2018009</code></pre>
</div>
<div class="sourceCode cell-code" id="cb145"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb145-1"><a href="#cb145-1" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(<span class="st">"P4_Local_2 模型比较:"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>[1] "P4_Local_2 模型比较:"</code></pre>
</div>
<div class="sourceCode cell-code" id="cb147"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb147-1"><a href="#cb147-1" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(p4_local_2_comparison[, <span class="fu">c</span>(<span class="st">"Type"</span>, <span class="st">"AIC"</span>, <span class="st">"BIC"</span>, <span class="st">"LogLik"</span>, <span class="st">"R2_marginal"</span>, <span class="st">"R2_conditional"</span>, <span class="st">"ICC"</span>)])</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>              Type      AIC      BIC    LogLik R2_marginal R2_conditional
7         Baseline 82404.06 82484.20 -41191.03   0.1800022      0.2601754
14  Fixed_Industry 81884.08 82037.07 -40921.04   0.2206665      0.3074628
21 Random_Industry 81921.02 82008.45 -40948.51   0.1798952      0.3284251
          ICC
7  0.09777245
14 0.11137242
21 0.18111088</code></pre>
</div>
</div>
</section>
<section id="第七步结果分析和可视化" class="level2">
<h2 class="anchored" data-anchor-id="第七步结果分析和可视化">第七步：结果分析和可视化</h2>
<div class="cell">
<div class="sourceCode cell-code" id="cb149"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb149-1"><a href="#cb149-1" aria-hidden="true" tabindex="-1"></a><span class="co"># ===== 结果分析和可视化 =====</span></span>
<span id="cb149-2"><a href="#cb149-2" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(ggplot2)</span>
<span id="cb149-3"><a href="#cb149-3" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(gridExtra)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>
Attaching package: 'gridExtra'</code></pre>
</div>
<div class="cell-output cell-output-stderr">
<pre><code>The following object is masked from 'package:dplyr':

    combine</code></pre>
</div>
<div class="sourceCode cell-code" id="cb152"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb152-1"><a href="#cb152-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 1. AIC/BIC比较图</span></span>
<span id="cb152-2"><a href="#cb152-2" aria-hidden="true" tabindex="-1"></a>aic_bic_plot <span class="ot">&lt;-</span> all_metrics <span class="sc">%&gt;%</span></span>
<span id="cb152-3"><a href="#cb152-3" aria-hidden="true" tabindex="-1"></a>  <span class="fu">select</span>(Model, Type, AIC, BIC) <span class="sc">%&gt;%</span></span>
<span id="cb152-4"><a href="#cb152-4" aria-hidden="true" tabindex="-1"></a>  tidyr<span class="sc">::</span><span class="fu">pivot_longer</span>(<span class="at">cols =</span> <span class="fu">c</span>(AIC, BIC), <span class="at">names_to =</span> <span class="st">"Metric"</span>, <span class="at">values_to =</span> <span class="st">"Value"</span>) <span class="sc">%&gt;%</span></span>
<span id="cb152-5"><a href="#cb152-5" aria-hidden="true" tabindex="-1"></a>  <span class="fu">mutate</span>(<span class="at">Model_Group =</span> <span class="fu">gsub</span>(<span class="st">"_baseline|_fixed|_random"</span>, <span class="st">""</span>, Model)) <span class="sc">%&gt;%</span></span>
<span id="cb152-6"><a href="#cb152-6" aria-hidden="true" tabindex="-1"></a>  <span class="fu">ggplot</span>(<span class="fu">aes</span>(<span class="at">x =</span> Model_Group, <span class="at">y =</span> Value, <span class="at">fill =</span> Type)) <span class="sc">+</span></span>
<span id="cb152-7"><a href="#cb152-7" aria-hidden="true" tabindex="-1"></a>  <span class="fu">geom_bar</span>(<span class="at">stat =</span> <span class="st">"identity"</span>, <span class="at">position =</span> <span class="st">"dodge"</span>) <span class="sc">+</span></span>
<span id="cb152-8"><a href="#cb152-8" aria-hidden="true" tabindex="-1"></a>  <span class="fu">facet_wrap</span>(<span class="sc">~</span>Metric, <span class="at">scales =</span> <span class="st">"free_y"</span>) <span class="sc">+</span></span>
<span id="cb152-9"><a href="#cb152-9" aria-hidden="true" tabindex="-1"></a>  <span class="fu">theme_minimal</span>() <span class="sc">+</span></span>
<span id="cb152-10"><a href="#cb152-10" aria-hidden="true" tabindex="-1"></a>  <span class="fu">theme</span>(<span class="at">axis.text.x =</span> <span class="fu">element_text</span>(<span class="at">angle =</span> <span class="dv">45</span>, <span class="at">hjust =</span> <span class="dv">1</span>)) <span class="sc">+</span></span>
<span id="cb152-11"><a href="#cb152-11" aria-hidden="true" tabindex="-1"></a>  <span class="fu">labs</span>(<span class="at">title =</span> <span class="st">"AIC和BIC比较"</span>,</span>
<span id="cb152-12"><a href="#cb152-12" aria-hidden="true" tabindex="-1"></a>       <span class="at">subtitle =</span> <span class="st">"较低的值表示更好的模型拟合"</span>,</span>
<span id="cb152-13"><a href="#cb152-13" aria-hidden="true" tabindex="-1"></a>       <span class="at">x =</span> <span class="st">"模型组"</span>, <span class="at">y =</span> <span class="st">"信息准则值"</span>) <span class="sc">+</span></span>
<span id="cb152-14"><a href="#cb152-14" aria-hidden="true" tabindex="-1"></a>  <span class="fu">scale_fill_manual</span>(<span class="at">values =</span> <span class="fu">c</span>(<span class="st">"Baseline"</span> <span class="ot">=</span> <span class="st">"#E74C3C"</span>,</span>
<span id="cb152-15"><a href="#cb152-15" aria-hidden="true" tabindex="-1"></a>                              <span class="st">"Fixed_Industry"</span> <span class="ot">=</span> <span class="st">"#3498DB"</span>,</span>
<span id="cb152-16"><a href="#cb152-16" aria-hidden="true" tabindex="-1"></a>                              <span class="st">"Random_Industry"</span> <span class="ot">=</span> <span class="st">"#2ECC71"</span>))</span>
<span id="cb152-17"><a href="#cb152-17" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb152-18"><a href="#cb152-18" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(aic_bic_plot)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<div>
<figure class="figure">
<p><img src="industry_11class_analysis_v2.1_files/figure-html/results_analysis_visualization-1.png" class="img-fluid figure-img" width="672"></p>
</figure>
</div>
</div>
<div class="sourceCode cell-code" id="cb153"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb153-1"><a href="#cb153-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 2. R²比较图</span></span>
<span id="cb153-2"><a href="#cb153-2" aria-hidden="true" tabindex="-1"></a>r2_plot <span class="ot">&lt;-</span> all_metrics <span class="sc">%&gt;%</span></span>
<span id="cb153-3"><a href="#cb153-3" aria-hidden="true" tabindex="-1"></a>  <span class="fu">select</span>(Model, Type, R2_marginal, R2_conditional) <span class="sc">%&gt;%</span></span>
<span id="cb153-4"><a href="#cb153-4" aria-hidden="true" tabindex="-1"></a>  tidyr<span class="sc">::</span><span class="fu">pivot_longer</span>(<span class="at">cols =</span> <span class="fu">c</span>(R2_marginal, R2_conditional), <span class="at">names_to =</span> <span class="st">"R2_Type"</span>, <span class="at">values_to =</span> <span class="st">"Value"</span>) <span class="sc">%&gt;%</span></span>
<span id="cb153-5"><a href="#cb153-5" aria-hidden="true" tabindex="-1"></a>  <span class="fu">mutate</span>(<span class="at">Model_Group =</span> <span class="fu">gsub</span>(<span class="st">"_baseline|_fixed|_random"</span>, <span class="st">""</span>, Model)) <span class="sc">%&gt;%</span></span>
<span id="cb153-6"><a href="#cb153-6" aria-hidden="true" tabindex="-1"></a>  <span class="fu">ggplot</span>(<span class="fu">aes</span>(<span class="at">x =</span> Model_Group, <span class="at">y =</span> Value, <span class="at">fill =</span> Type)) <span class="sc">+</span></span>
<span id="cb153-7"><a href="#cb153-7" aria-hidden="true" tabindex="-1"></a>  <span class="fu">geom_bar</span>(<span class="at">stat =</span> <span class="st">"identity"</span>, <span class="at">position =</span> <span class="st">"dodge"</span>) <span class="sc">+</span></span>
<span id="cb153-8"><a href="#cb153-8" aria-hidden="true" tabindex="-1"></a>  <span class="fu">facet_wrap</span>(<span class="sc">~</span>R2_Type, <span class="at">scales =</span> <span class="st">"free_y"</span>) <span class="sc">+</span></span>
<span id="cb153-9"><a href="#cb153-9" aria-hidden="true" tabindex="-1"></a>  <span class="fu">theme_minimal</span>() <span class="sc">+</span></span>
<span id="cb153-10"><a href="#cb153-10" aria-hidden="true" tabindex="-1"></a>  <span class="fu">theme</span>(<span class="at">axis.text.x =</span> <span class="fu">element_text</span>(<span class="at">angle =</span> <span class="dv">45</span>, <span class="at">hjust =</span> <span class="dv">1</span>)) <span class="sc">+</span></span>
<span id="cb153-11"><a href="#cb153-11" aria-hidden="true" tabindex="-1"></a>  <span class="fu">labs</span>(<span class="at">title =</span> <span class="st">"R²比较"</span>,</span>
<span id="cb153-12"><a href="#cb153-12" aria-hidden="true" tabindex="-1"></a>       <span class="at">subtitle =</span> <span class="st">"较高的值表示更好的解释能力"</span>,</span>
<span id="cb153-13"><a href="#cb153-13" aria-hidden="true" tabindex="-1"></a>       <span class="at">x =</span> <span class="st">"模型组"</span>, <span class="at">y =</span> <span class="st">"R²值"</span>) <span class="sc">+</span></span>
<span id="cb153-14"><a href="#cb153-14" aria-hidden="true" tabindex="-1"></a>  <span class="fu">scale_fill_manual</span>(<span class="at">values =</span> <span class="fu">c</span>(<span class="st">"Baseline"</span> <span class="ot">=</span> <span class="st">"#E74C3C"</span>,</span>
<span id="cb153-15"><a href="#cb153-15" aria-hidden="true" tabindex="-1"></a>                              <span class="st">"Fixed_Industry"</span> <span class="ot">=</span> <span class="st">"#3498DB"</span>,</span>
<span id="cb153-16"><a href="#cb153-16" aria-hidden="true" tabindex="-1"></a>                              <span class="st">"Random_Industry"</span> <span class="ot">=</span> <span class="st">"#2ECC71"</span>))</span>
<span id="cb153-17"><a href="#cb153-17" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb153-18"><a href="#cb153-18" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(r2_plot)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<div>
<figure class="figure">
<p><img src="industry_11class_analysis_v2.1_files/figure-html/results_analysis_visualization-2.png" class="img-fluid figure-img" width="672"></p>
</figure>
</div>
</div>
<div class="sourceCode cell-code" id="cb154"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb154-1"><a href="#cb154-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 3. ICC比较图</span></span>
<span id="cb154-2"><a href="#cb154-2" aria-hidden="true" tabindex="-1"></a>icc_plot <span class="ot">&lt;-</span> all_metrics <span class="sc">%&gt;%</span></span>
<span id="cb154-3"><a href="#cb154-3" aria-hidden="true" tabindex="-1"></a>  <span class="fu">mutate</span>(<span class="at">Model_Group =</span> <span class="fu">gsub</span>(<span class="st">"_baseline|_fixed|_random"</span>, <span class="st">""</span>, Model)) <span class="sc">%&gt;%</span></span>
<span id="cb154-4"><a href="#cb154-4" aria-hidden="true" tabindex="-1"></a>  <span class="fu">ggplot</span>(<span class="fu">aes</span>(<span class="at">x =</span> Model_Group, <span class="at">y =</span> ICC, <span class="at">fill =</span> Type)) <span class="sc">+</span></span>
<span id="cb154-5"><a href="#cb154-5" aria-hidden="true" tabindex="-1"></a>  <span class="fu">geom_bar</span>(<span class="at">stat =</span> <span class="st">"identity"</span>, <span class="at">position =</span> <span class="st">"dodge"</span>) <span class="sc">+</span></span>
<span id="cb154-6"><a href="#cb154-6" aria-hidden="true" tabindex="-1"></a>  <span class="fu">theme_minimal</span>() <span class="sc">+</span></span>
<span id="cb154-7"><a href="#cb154-7" aria-hidden="true" tabindex="-1"></a>  <span class="fu">theme</span>(<span class="at">axis.text.x =</span> <span class="fu">element_text</span>(<span class="at">angle =</span> <span class="dv">45</span>, <span class="at">hjust =</span> <span class="dv">1</span>)) <span class="sc">+</span></span>
<span id="cb154-8"><a href="#cb154-8" aria-hidden="true" tabindex="-1"></a>  <span class="fu">labs</span>(<span class="at">title =</span> <span class="st">"ICC (组内相关系数) 比较"</span>,</span>
<span id="cb154-9"><a href="#cb154-9" aria-hidden="true" tabindex="-1"></a>       <span class="at">subtitle =</span> <span class="st">"显示随机效应的重要性"</span>,</span>
<span id="cb154-10"><a href="#cb154-10" aria-hidden="true" tabindex="-1"></a>       <span class="at">x =</span> <span class="st">"模型组"</span>, <span class="at">y =</span> <span class="st">"ICC值"</span>) <span class="sc">+</span></span>
<span id="cb154-11"><a href="#cb154-11" aria-hidden="true" tabindex="-1"></a>  <span class="fu">scale_fill_manual</span>(<span class="at">values =</span> <span class="fu">c</span>(<span class="st">"Baseline"</span> <span class="ot">=</span> <span class="st">"#E74C3C"</span>,</span>
<span id="cb154-12"><a href="#cb154-12" aria-hidden="true" tabindex="-1"></a>                              <span class="st">"Fixed_Industry"</span> <span class="ot">=</span> <span class="st">"#3498DB"</span>,</span>
<span id="cb154-13"><a href="#cb154-13" aria-hidden="true" tabindex="-1"></a>                              <span class="st">"Random_Industry"</span> <span class="ot">=</span> <span class="st">"#2ECC71"</span>))</span>
<span id="cb154-14"><a href="#cb154-14" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb154-15"><a href="#cb154-15" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(icc_plot)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<div>
<figure class="figure">
<p><img src="industry_11class_analysis_v2.1_files/figure-html/results_analysis_visualization-3.png" class="img-fluid figure-img" width="672"></p>
</figure>
</div>
</div>
<div class="sourceCode cell-code" id="cb155"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb155-1"><a href="#cb155-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 4. 创建性能改善/恶化统计</span></span>
<span id="cb155-2"><a href="#cb155-2" aria-hidden="true" tabindex="-1"></a>performance_summary <span class="ot">&lt;-</span> all_metrics <span class="sc">%&gt;%</span></span>
<span id="cb155-3"><a href="#cb155-3" aria-hidden="true" tabindex="-1"></a>  <span class="fu">mutate</span>(<span class="at">Model_Group =</span> <span class="fu">gsub</span>(<span class="st">"_baseline|_fixed|_random"</span>, <span class="st">""</span>, Model)) <span class="sc">%&gt;%</span></span>
<span id="cb155-4"><a href="#cb155-4" aria-hidden="true" tabindex="-1"></a>  <span class="fu">group_by</span>(Model_Group) <span class="sc">%&gt;%</span></span>
<span id="cb155-5"><a href="#cb155-5" aria-hidden="true" tabindex="-1"></a>  <span class="fu">summarise</span>(</span>
<span id="cb155-6"><a href="#cb155-6" aria-hidden="true" tabindex="-1"></a>    <span class="at">Baseline_AIC =</span> AIC[Type <span class="sc">==</span> <span class="st">"Baseline"</span>],</span>
<span id="cb155-7"><a href="#cb155-7" aria-hidden="true" tabindex="-1"></a>    <span class="at">Fixed_AIC =</span> AIC[Type <span class="sc">==</span> <span class="st">"Fixed_Industry"</span>],</span>
<span id="cb155-8"><a href="#cb155-8" aria-hidden="true" tabindex="-1"></a>    <span class="at">Random_AIC =</span> AIC[Type <span class="sc">==</span> <span class="st">"Random_Industry"</span>],</span>
<span id="cb155-9"><a href="#cb155-9" aria-hidden="true" tabindex="-1"></a>    <span class="at">Fixed_AIC_Change =</span> Fixed_AIC <span class="sc">-</span> Baseline_AIC,</span>
<span id="cb155-10"><a href="#cb155-10" aria-hidden="true" tabindex="-1"></a>    <span class="at">Random_AIC_Change =</span> Random_AIC <span class="sc">-</span> Baseline_AIC,</span>
<span id="cb155-11"><a href="#cb155-11" aria-hidden="true" tabindex="-1"></a>    <span class="at">Fixed_Worse =</span> Fixed_AIC_Change <span class="sc">&gt;</span> <span class="dv">0</span>,</span>
<span id="cb155-12"><a href="#cb155-12" aria-hidden="true" tabindex="-1"></a>    <span class="at">Random_Worse =</span> Random_AIC_Change <span class="sc">&gt;</span> <span class="dv">0</span>,</span>
<span id="cb155-13"><a href="#cb155-13" aria-hidden="true" tabindex="-1"></a>    <span class="at">.groups =</span> <span class="st">'drop'</span></span>
<span id="cb155-14"><a href="#cb155-14" aria-hidden="true" tabindex="-1"></a>  )</span>
<span id="cb155-15"><a href="#cb155-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb155-16"><a href="#cb155-16" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"</span><span class="sc">\n</span><span class="st">=== 性能变化总结 ===</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
=== 性能变化总结 ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb157"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb157-1"><a href="#cb157-1" aria-hidden="true" tabindex="-1"></a><span class="fu">print</span>(performance_summary)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code># A tibble: 7 × 8
  Model_Group  Baseline_AIC Fixed_AIC Random_AIC Fixed_AIC_Change
  &lt;chr&gt;               &lt;dbl&gt;     &lt;dbl&gt;      &lt;dbl&gt;            &lt;dbl&gt;
1 P3_1               83024.    82417.     82456.            -608.
2 P3_2               82408.    81876.     81913.            -531.
3 P3_3               82345.    81809.     81847.            -536.
4 P4_central_1       83042.    82416.     82455.            -626.
5 P4_central_2       82401.    81873.     81910.            -528.
6 P4_local_1         83030.    82419.     82458.            -611.
7 P4_local_2         82404.    81884.     81921.            -520.
# ℹ 3 more variables: Random_AIC_Change &lt;dbl&gt;, Fixed_Worse &lt;lgl&gt;,
#   Random_Worse &lt;lgl&gt;</code></pre>
</div>
<div class="sourceCode cell-code" id="cb159"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb159-1"><a href="#cb159-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 统计有多少模型在添加行业分类后性能变差</span></span>
<span id="cb159-2"><a href="#cb159-2" aria-hidden="true" tabindex="-1"></a>fixed_worse_count <span class="ot">&lt;-</span> <span class="fu">sum</span>(performance_summary<span class="sc">$</span>Fixed_Worse)</span>
<span id="cb159-3"><a href="#cb159-3" aria-hidden="true" tabindex="-1"></a>random_worse_count <span class="ot">&lt;-</span> <span class="fu">sum</span>(performance_summary<span class="sc">$</span>Random_Worse)</span>
<span id="cb159-4"><a href="#cb159-4" aria-hidden="true" tabindex="-1"></a>total_models <span class="ot">&lt;-</span> <span class="fu">nrow</span>(performance_summary)</span>
<span id="cb159-5"><a href="#cb159-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb159-6"><a href="#cb159-6" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"</span><span class="sc">\n</span><span class="st">=== 最终结果统计 ===</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>
=== 最终结果统计 ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb161"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb161-1"><a href="#cb161-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"总模型组数:"</span>, total_models, <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>总模型组数: 7 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb163"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb163-1"><a href="#cb163-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"添加固定行业效应后AIC变差的模型数:"</span>, fixed_worse_count, <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>添加固定行业效应后AIC变差的模型数: 0 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb165"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb165-1"><a href="#cb165-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"添加随机行业效应后AIC变差的模型数:"</span>, random_worse_count, <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>添加随机行业效应后AIC变差的模型数: 0 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb167"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb167-1"><a href="#cb167-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"固定效应恶化比例:"</span>, <span class="fu">round</span>(fixed_worse_count<span class="sc">/</span>total_models<span class="sc">*</span><span class="dv">100</span>, <span class="dv">2</span>), <span class="st">"%</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>固定效应恶化比例: 0 %</code></pre>
</div>
<div class="sourceCode cell-code" id="cb169"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb169-1"><a href="#cb169-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"随机效应恶化比例:"</span>, <span class="fu">round</span>(random_worse_count<span class="sc">/</span>total_models<span class="sc">*</span><span class="dv">100</span>, <span class="dv">2</span>), <span class="st">"%</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>随机效应恶化比例: 0 %</code></pre>
</div>
</div>
</section>
<section id="第八步详细结论分析" class="level2">
<h2 class="anchored" data-anchor-id="第八步详细结论分析">第八步：详细结论分析</h2>
<div class="cell">
<div class="sourceCode cell-code" id="cb171"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb171-1"><a href="#cb171-1" aria-hidden="true" tabindex="-1"></a><span class="co"># ===== 详细结论分析 =====</span></span>
<span id="cb171-2"><a href="#cb171-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb171-3"><a href="#cb171-3" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"=== 模型比较分析结论 ===</span><span class="sc">\n\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>=== 模型比较分析结论 ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb173"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb173-1"><a href="#cb173-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 1. 整体性能比较</span></span>
<span id="cb173-2"><a href="#cb173-2" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"1. 整体性能比较:</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>1. 整体性能比较:</code></pre>
</div>
<div class="sourceCode cell-code" id="cb175"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb175-1"><a href="#cb175-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 我们比较了21个模型（7个基准模型 + 7个固定效应模型 + 7个随机效应模型）</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 我们比较了21个模型（7个基准模型 + 7个固定效应模型 + 7个随机效应模型）</code></pre>
</div>
<div class="sourceCode cell-code" id="cb177"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb177-1"><a href="#cb177-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 基准模型：只包含省份/城市随机效应，不包含行业分类</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 基准模型：只包含省份/城市随机效应，不包含行业分类</code></pre>
</div>
<div class="sourceCode cell-code" id="cb179"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb179-1"><a href="#cb179-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 固定效应模型：在基准模型基础上添加industry_type11作为固定效应</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 固定效应模型：在基准模型基础上添加industry_type11作为固定效应</code></pre>
</div>
<div class="sourceCode cell-code" id="cb181"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb181-1"><a href="#cb181-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 随机效应模型：在基准模型基础上添加industry_type11作为随机效应</span><span class="sc">\n\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 随机效应模型：在基准模型基础上添加industry_type11作为随机效应</code></pre>
</div>
<div class="sourceCode cell-code" id="cb183"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb183-1"><a href="#cb183-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 2. AIC/BIC分析</span></span>
<span id="cb183-2"><a href="#cb183-2" aria-hidden="true" tabindex="-1"></a>aic_improvements_fixed <span class="ot">&lt;-</span> <span class="fu">sum</span>(performance_summary<span class="sc">$</span>Fixed_AIC_Change <span class="sc">&lt;</span> <span class="dv">0</span>)</span>
<span id="cb183-3"><a href="#cb183-3" aria-hidden="true" tabindex="-1"></a>aic_improvements_random <span class="ot">&lt;-</span> <span class="fu">sum</span>(performance_summary<span class="sc">$</span>Random_AIC_Change <span class="sc">&lt;</span> <span class="dv">0</span>)</span>
<span id="cb183-4"><a href="#cb183-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb183-5"><a href="#cb183-5" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"2. AIC/BIC信息准则分析:</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>2. AIC/BIC信息准则分析:</code></pre>
</div>
<div class="sourceCode cell-code" id="cb185"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb185-1"><a href="#cb185-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - AIC/BIC越低表示模型拟合越好</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - AIC/BIC越低表示模型拟合越好</code></pre>
</div>
<div class="sourceCode cell-code" id="cb187"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb187-1"><a href="#cb187-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 固定效应模型中AIC改善的模型数:"</span>, aic_improvements_fixed, <span class="st">"/"</span>, total_models, <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 固定效应模型中AIC改善的模型数: 7 / 7 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb189"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb189-1"><a href="#cb189-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 随机效应模型中AIC改善的模型数:"</span>, aic_improvements_random, <span class="st">"/"</span>, total_models, <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 随机效应模型中AIC改善的模型数: 7 / 7 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb191"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb191-1"><a href="#cb191-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 固定效应改善比例:"</span>, <span class="fu">round</span>(aic_improvements_fixed<span class="sc">/</span>total_models<span class="sc">*</span><span class="dv">100</span>, <span class="dv">2</span>), <span class="st">"%</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 固定效应改善比例: 100 %</code></pre>
</div>
<div class="sourceCode cell-code" id="cb193"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb193-1"><a href="#cb193-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 随机效应改善比例:"</span>, <span class="fu">round</span>(aic_improvements_random<span class="sc">/</span>total_models<span class="sc">*</span><span class="dv">100</span>, <span class="dv">2</span>), <span class="st">"%</span><span class="sc">\n\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 随机效应改善比例: 100 %</code></pre>
</div>
<div class="sourceCode cell-code" id="cb195"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb195-1"><a href="#cb195-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 3. R²分析</span></span>
<span id="cb195-2"><a href="#cb195-2" aria-hidden="true" tabindex="-1"></a>r2_analysis <span class="ot">&lt;-</span> all_metrics <span class="sc">%&gt;%</span></span>
<span id="cb195-3"><a href="#cb195-3" aria-hidden="true" tabindex="-1"></a>  <span class="fu">mutate</span>(<span class="at">Model_Group =</span> <span class="fu">gsub</span>(<span class="st">"_baseline|_fixed|_random"</span>, <span class="st">""</span>, Model)) <span class="sc">%&gt;%</span></span>
<span id="cb195-4"><a href="#cb195-4" aria-hidden="true" tabindex="-1"></a>  <span class="fu">group_by</span>(Model_Group) <span class="sc">%&gt;%</span></span>
<span id="cb195-5"><a href="#cb195-5" aria-hidden="true" tabindex="-1"></a>  <span class="fu">summarise</span>(</span>
<span id="cb195-6"><a href="#cb195-6" aria-hidden="true" tabindex="-1"></a>    <span class="at">Baseline_R2_marginal =</span> R2_marginal[Type <span class="sc">==</span> <span class="st">"Baseline"</span>],</span>
<span id="cb195-7"><a href="#cb195-7" aria-hidden="true" tabindex="-1"></a>    <span class="at">Fixed_R2_marginal =</span> R2_marginal[Type <span class="sc">==</span> <span class="st">"Fixed_Industry"</span>],</span>
<span id="cb195-8"><a href="#cb195-8" aria-hidden="true" tabindex="-1"></a>    <span class="at">Random_R2_marginal =</span> R2_marginal[Type <span class="sc">==</span> <span class="st">"Random_Industry"</span>],</span>
<span id="cb195-9"><a href="#cb195-9" aria-hidden="true" tabindex="-1"></a>    <span class="at">Fixed_R2_improvement =</span> Fixed_R2_marginal <span class="sc">-</span> Baseline_R2_marginal,</span>
<span id="cb195-10"><a href="#cb195-10" aria-hidden="true" tabindex="-1"></a>    <span class="at">Random_R2_improvement =</span> Random_R2_marginal <span class="sc">-</span> Baseline_R2_marginal,</span>
<span id="cb195-11"><a href="#cb195-11" aria-hidden="true" tabindex="-1"></a>    <span class="at">.groups =</span> <span class="st">'drop'</span></span>
<span id="cb195-12"><a href="#cb195-12" aria-hidden="true" tabindex="-1"></a>  )</span>
<span id="cb195-13"><a href="#cb195-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb195-14"><a href="#cb195-14" aria-hidden="true" tabindex="-1"></a>r2_fixed_improvements <span class="ot">&lt;-</span> <span class="fu">sum</span>(r2_analysis<span class="sc">$</span>Fixed_R2_improvement <span class="sc">&gt;</span> <span class="dv">0</span>)</span>
<span id="cb195-15"><a href="#cb195-15" aria-hidden="true" tabindex="-1"></a>r2_random_improvements <span class="ot">&lt;-</span> <span class="fu">sum</span>(r2_analysis<span class="sc">$</span>Random_R2_improvement <span class="sc">&gt;</span> <span class="dv">0</span>)</span>
<span id="cb195-16"><a href="#cb195-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb195-17"><a href="#cb195-17" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"3. R²解释能力分析:</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>3. R²解释能力分析:</code></pre>
</div>
<div class="sourceCode cell-code" id="cb197"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb197-1"><a href="#cb197-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - R²越高表示模型解释能力越强</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - R²越高表示模型解释能力越强</code></pre>
</div>
<div class="sourceCode cell-code" id="cb199"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb199-1"><a href="#cb199-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 固定效应模型中边际R²改善的模型数:"</span>, r2_fixed_improvements, <span class="st">"/"</span>, total_models, <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 固定效应模型中边际R²改善的模型数: 7 / 7 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb201"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb201-1"><a href="#cb201-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 随机效应模型中边际R²改善的模型数:"</span>, r2_random_improvements, <span class="st">"/"</span>, total_models, <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 随机效应模型中边际R²改善的模型数: 6 / 7 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb203"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb203-1"><a href="#cb203-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 固定效应R²改善比例:"</span>, <span class="fu">round</span>(r2_fixed_improvements<span class="sc">/</span>total_models<span class="sc">*</span><span class="dv">100</span>, <span class="dv">2</span>), <span class="st">"%</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 固定效应R²改善比例: 100 %</code></pre>
</div>
<div class="sourceCode cell-code" id="cb205"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb205-1"><a href="#cb205-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 随机效应R²改善比例:"</span>, <span class="fu">round</span>(r2_random_improvements<span class="sc">/</span>total_models<span class="sc">*</span><span class="dv">100</span>, <span class="dv">2</span>), <span class="st">"%</span><span class="sc">\n\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 随机效应R²改善比例: 85.71 %</code></pre>
</div>
<div class="sourceCode cell-code" id="cb207"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb207-1"><a href="#cb207-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 4. 似然比检验分析</span></span>
<span id="cb207-2"><a href="#cb207-2" aria-hidden="true" tabindex="-1"></a>significant_fixed <span class="ot">&lt;-</span> <span class="fu">sum</span>(<span class="fu">grepl</span>(<span class="st">"Fixed"</span>, lr_results<span class="sc">$</span>Comparison) <span class="sc">&amp;</span> lr_results<span class="sc">$</span>Significant)</span>
<span id="cb207-3"><a href="#cb207-3" aria-hidden="true" tabindex="-1"></a>significant_random <span class="ot">&lt;-</span> <span class="fu">sum</span>(<span class="fu">grepl</span>(<span class="st">"Random"</span>, lr_results<span class="sc">$</span>Comparison) <span class="sc">&amp;</span> lr_results<span class="sc">$</span>Significant)</span>
<span id="cb207-4"><a href="#cb207-4" aria-hidden="true" tabindex="-1"></a>total_fixed_tests <span class="ot">&lt;-</span> <span class="fu">sum</span>(<span class="fu">grepl</span>(<span class="st">"Fixed"</span>, lr_results<span class="sc">$</span>Comparison))</span>
<span id="cb207-5"><a href="#cb207-5" aria-hidden="true" tabindex="-1"></a>total_random_tests <span class="ot">&lt;-</span> <span class="fu">sum</span>(<span class="fu">grepl</span>(<span class="st">"Random"</span>, lr_results<span class="sc">$</span>Comparison))</span>
<span id="cb207-6"><a href="#cb207-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb207-7"><a href="#cb207-7" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"4. 似然比检验分析:</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>4. 似然比检验分析:</code></pre>
</div>
<div class="sourceCode cell-code" id="cb209"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb209-1"><a href="#cb209-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 检验添加行业分类是否显著改善模型拟合</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 检验添加行业分类是否显著改善模型拟合</code></pre>
</div>
<div class="sourceCode cell-code" id="cb211"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb211-1"><a href="#cb211-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 固定效应显著改善的模型数:"</span>, significant_fixed, <span class="st">"/"</span>, total_fixed_tests, <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 固定效应显著改善的模型数: 7 / 7 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb213"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb213-1"><a href="#cb213-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 随机效应显著改善的模型数:"</span>, significant_random, <span class="st">"/"</span>, total_random_tests, <span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 随机效应显著改善的模型数: 7 / 7 </code></pre>
</div>
<div class="sourceCode cell-code" id="cb215"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb215-1"><a href="#cb215-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 固定效应显著改善比例:"</span>, <span class="fu">round</span>(significant_fixed<span class="sc">/</span>total_fixed_tests<span class="sc">*</span><span class="dv">100</span>, <span class="dv">2</span>), <span class="st">"%</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 固定效应显著改善比例: 100 %</code></pre>
</div>
<div class="sourceCode cell-code" id="cb217"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb217-1"><a href="#cb217-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"   - 随机效应显著改善比例:"</span>, <span class="fu">round</span>(significant_random<span class="sc">/</span>total_random_tests<span class="sc">*</span><span class="dv">100</span>, <span class="dv">2</span>), <span class="st">"%</span><span class="sc">\n\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   - 随机效应显著改善比例: 100 %</code></pre>
</div>
<div class="sourceCode cell-code" id="cb219"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb219-1"><a href="#cb219-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 5. 最终结论</span></span>
<span id="cb219-2"><a href="#cb219-2" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"5. 最终结论:</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>5. 最终结论:</code></pre>
</div>
<div class="sourceCode cell-code" id="cb221"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb221-1"><a href="#cb221-1" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span>(fixed_worse_count <span class="sc">&gt;</span> total_models<span class="sc">/</span><span class="dv">2</span>) {</span>
<span id="cb221-2"><a href="#cb221-2" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">"   ✓ 添加industry_type11作为固定效应在大多数情况下降低了模型性能</span><span class="sc">\n</span><span class="st">"</span>)</span>
<span id="cb221-3"><a href="#cb221-3" aria-hidden="true" tabindex="-1"></a>} <span class="cf">else</span> {</span>
<span id="cb221-4"><a href="#cb221-4" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">"   ✗ 添加industry_type11作为固定效应在大多数情况下改善了模型性能</span><span class="sc">\n</span><span class="st">"</span>)</span>
<span id="cb221-5"><a href="#cb221-5" aria-hidden="true" tabindex="-1"></a>}</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   ✗ 添加industry_type11作为固定效应在大多数情况下改善了模型性能</code></pre>
</div>
<div class="sourceCode cell-code" id="cb223"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb223-1"><a href="#cb223-1" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span>(random_worse_count <span class="sc">&gt;</span> total_models<span class="sc">/</span><span class="dv">2</span>) {</span>
<span id="cb223-2"><a href="#cb223-2" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">"   ✓ 添加industry_type11作为随机效应在大多数情况下降低了模型性能</span><span class="sc">\n</span><span class="st">"</span>)</span>
<span id="cb223-3"><a href="#cb223-3" aria-hidden="true" tabindex="-1"></a>} <span class="cf">else</span> {</span>
<span id="cb223-4"><a href="#cb223-4" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">"   ✗ 添加industry_type11作为随机效应在大多数情况下改善了模型性能</span><span class="sc">\n</span><span class="st">"</span>)</span>
<span id="cb223-5"><a href="#cb223-5" aria-hidden="true" tabindex="-1"></a>}</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   ✗ 添加industry_type11作为随机效应在大多数情况下改善了模型性能</code></pre>
</div>
<div class="sourceCode cell-code" id="cb225"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb225-1"><a href="#cb225-1" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span>(aic_improvements_fixed <span class="sc">&lt;</span> total_models<span class="sc">/</span><span class="dv">2</span> <span class="sc">&amp;&amp;</span> aic_improvements_random <span class="sc">&lt;</span> total_models<span class="sc">/</span><span class="dv">2</span>) {</span>
<span id="cb225-2"><a href="#cb225-2" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">"   ✓ 根据AIC准则，添加行业分类总体上降低了模型拟合质量</span><span class="sc">\n</span><span class="st">"</span>)</span>
<span id="cb225-3"><a href="#cb225-3" aria-hidden="true" tabindex="-1"></a>} <span class="cf">else</span> {</span>
<span id="cb225-4"><a href="#cb225-4" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">"   ✗ 根据AIC准则，添加行业分类总体上改善了模型拟合质量</span><span class="sc">\n</span><span class="st">"</span>)</span>
<span id="cb225-5"><a href="#cb225-5" aria-hidden="true" tabindex="-1"></a>}</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   ✗ 根据AIC准则，添加行业分类总体上改善了模型拟合质量</code></pre>
</div>
<div class="sourceCode cell-code" id="cb227"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb227-1"><a href="#cb227-1" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span>(significant_fixed <span class="sc">&lt;</span> total_fixed_tests<span class="sc">/</span><span class="dv">2</span> <span class="sc">&amp;&amp;</span> significant_random <span class="sc">&lt;</span> total_random_tests<span class="sc">/</span><span class="dv">2</span>) {</span>
<span id="cb227-2"><a href="#cb227-2" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">"   ✓ 似然比检验显示，大多数情况下添加行业分类没有显著改善模型</span><span class="sc">\n</span><span class="st">"</span>)</span>
<span id="cb227-3"><a href="#cb227-3" aria-hidden="true" tabindex="-1"></a>} <span class="cf">else</span> {</span>
<span id="cb227-4"><a href="#cb227-4" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">"   ✗ 似然比检验显示，大多数情况下添加行业分类显著改善了模型</span><span class="sc">\n</span><span class="st">"</span>)</span>
<span id="cb227-5"><a href="#cb227-5" aria-hidden="true" tabindex="-1"></a>}</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>   ✗ 似然比检验显示，大多数情况下添加行业分类显著改善了模型</code></pre>
</div>
<div class="sourceCode cell-code" id="cb229"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb229-1"><a href="#cb229-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="sourceCode cell-code" id="cb230"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb230-1"><a href="#cb230-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"=== 研究假设验证 ===</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>=== 研究假设验证 ===</code></pre>
</div>
<div class="sourceCode cell-code" id="cb232"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb232-1"><a href="#cb232-1" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"研究假设：添加industry_type11会降低模型性能</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>研究假设：添加industry_type11会降低模型性能</code></pre>
</div>
<div class="sourceCode cell-code" id="cb234"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb234-1"><a href="#cb234-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 计算支持假设的证据比例</span></span>
<span id="cb234-2"><a href="#cb234-2" aria-hidden="true" tabindex="-1"></a>evidence_count <span class="ot">&lt;-</span> <span class="dv">0</span></span>
<span id="cb234-3"><a href="#cb234-3" aria-hidden="true" tabindex="-1"></a>total_evidence <span class="ot">&lt;-</span> <span class="dv">4</span></span>
<span id="cb234-4"><a href="#cb234-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb234-5"><a href="#cb234-5" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span>(fixed_worse_count <span class="sc">&gt;</span> total_models<span class="sc">/</span><span class="dv">2</span>) evidence_count <span class="ot">&lt;-</span> evidence_count <span class="sc">+</span> <span class="dv">1</span></span>
<span id="cb234-6"><a href="#cb234-6" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span>(random_worse_count <span class="sc">&gt;</span> total_models<span class="sc">/</span><span class="dv">2</span>) evidence_count <span class="ot">&lt;-</span> evidence_count <span class="sc">+</span> <span class="dv">1</span></span>
<span id="cb234-7"><a href="#cb234-7" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span>(aic_improvements_fixed <span class="sc">&lt;</span> total_models<span class="sc">/</span><span class="dv">2</span> <span class="sc">&amp;&amp;</span> aic_improvements_random <span class="sc">&lt;</span> total_models<span class="sc">/</span><span class="dv">2</span>) evidence_count <span class="ot">&lt;-</span> evidence_count <span class="sc">+</span> <span class="dv">1</span></span>
<span id="cb234-8"><a href="#cb234-8" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span>(significant_fixed <span class="sc">&lt;</span> total_fixed_tests<span class="sc">/</span><span class="dv">2</span> <span class="sc">&amp;&amp;</span> significant_random <span class="sc">&lt;</span> total_random_tests<span class="sc">/</span><span class="dv">2</span>) evidence_count <span class="ot">&lt;-</span> evidence_count <span class="sc">+</span> <span class="dv">1</span></span>
<span id="cb234-9"><a href="#cb234-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb234-10"><a href="#cb234-10" aria-hidden="true" tabindex="-1"></a>support_percentage <span class="ot">&lt;-</span> <span class="fu">round</span>(evidence_count<span class="sc">/</span>total_evidence<span class="sc">*</span><span class="dv">100</span>, <span class="dv">2</span>)</span>
<span id="cb234-11"><a href="#cb234-11" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb234-12"><a href="#cb234-12" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">"支持假设的证据比例:"</span>, support_percentage, <span class="st">"%</span><span class="sc">\n</span><span class="st">"</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>支持假设的证据比例: 0 %</code></pre>
</div>
<div class="sourceCode cell-code" id="cb236"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb236-1"><a href="#cb236-1" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span>(support_percentage <span class="sc">&gt;=</span> <span class="dv">75</span>) {</span>
<span id="cb236-2"><a href="#cb236-2" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">"结论：强烈支持研究假设 - 添加industry_type11显著降低了模型性能</span><span class="sc">\n</span><span class="st">"</span>)</span>
<span id="cb236-3"><a href="#cb236-3" aria-hidden="true" tabindex="-1"></a>} <span class="cf">else</span> <span class="cf">if</span>(support_percentage <span class="sc">&gt;=</span> <span class="dv">50</span>) {</span>
<span id="cb236-4"><a href="#cb236-4" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">"结论：部分支持研究假设 - 添加industry_type11在某些方面降低了模型性能</span><span class="sc">\n</span><span class="st">"</span>)</span>
<span id="cb236-5"><a href="#cb236-5" aria-hidden="true" tabindex="-1"></a>} <span class="cf">else</span> {</span>
<span id="cb236-6"><a href="#cb236-6" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">"结论：不支持研究假设 - 添加industry_type11总体上改善了模型性能</span><span class="sc">\n</span><span class="st">"</span>)</span>
<span id="cb236-7"><a href="#cb236-7" aria-hidden="true" tabindex="-1"></a>}</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stdout">
<pre><code>结论：不支持研究假设 - 添加industry_type11总体上改善了模型性能</code></pre>
</div>
</div>
</section>
</section>
<section id="原始混合效应模型-lmer---保留原有分析" class="level1">
<h1>原始混合效应模型 (LMER) - 保留原有分析</h1>
<section id="基础混合效应模型" class="level2">
<h2 class="anchored" data-anchor-id="基础混合效应模型">基础混合效应模型</h2>
<div class="cell">
<div class="sourceCode cell-code" id="cb238"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb238-1"><a href="#cb238-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 基础混合效应模型 - 省份随机效应</span></span>
<span id="cb238-2"><a href="#cb238-2" aria-hidden="true" tabindex="-1"></a>p3mix1_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> connection_num <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> </span>
<span id="cb238-3"><a href="#cb238-3" aria-hidden="true" tabindex="-1"></a>                  RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY), </span>
<span id="cb238-4"><a href="#cb238-4" aria-hidden="true" tabindex="-1"></a>                  <span class="at">data =</span> dta1_11class)</span>
<span id="cb238-5"><a href="#cb238-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb238-6"><a href="#cb238-6" aria-hidden="true" tabindex="-1"></a>p3mix2_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> after_first_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> </span>
<span id="cb238-7"><a href="#cb238-7" aria-hidden="true" tabindex="-1"></a>                  RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY), </span>
<span id="cb238-8"><a href="#cb238-8" aria-hidden="true" tabindex="-1"></a>                  <span class="at">data =</span> dta1_11class)</span>
<span id="cb238-9"><a href="#cb238-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb238-10"><a href="#cb238-10" aria-hidden="true" tabindex="-1"></a>p3mix3_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> after_first_inspection <span class="sc">*</span> connection_num <span class="sc">+</span> </span>
<span id="cb238-11"><a href="#cb238-11" aria-hidden="true" tabindex="-1"></a>                  ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY), </span>
<span id="cb238-12"><a href="#cb238-12" aria-hidden="true" tabindex="-1"></a>                  <span class="at">data =</span> dta1_11class)</span>
<span id="cb238-13"><a href="#cb238-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb238-14"><a href="#cb238-14" aria-hidden="true" tabindex="-1"></a><span class="co"># 使用tab_model输出结果</span></span>
<span id="cb238-15"><a href="#cb238-15" aria-hidden="true" tabindex="-1"></a><span class="fu">tab_model</span>(p3mix1_11, p3mix2_11, p3mix3_11,</span>
<span id="cb238-16"><a href="#cb238-16" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"P3 Mixed Effects Models (11-Class) - Province Random Effects"</span>,</span>
<span id="cb238-17"><a href="#cb238-17" aria-hidden="true" tabindex="-1"></a>          <span class="at">dv.labels =</span> <span class="st">"Environmental Information Disclosure"</span>,</span>
<span id="cb238-18"><a href="#cb238-18" aria-hidden="true" tabindex="-1"></a>          <span class="at">rm.terms =</span> <span class="fu">c</span>(<span class="st">"as.factor"</span>),</span>
<span id="cb238-19"><a href="#cb238-19" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.reflvl =</span> <span class="cn">TRUE</span>,</span>
<span id="cb238-20"><a href="#cb238-20" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.icc =</span> <span class="cn">TRUE</span>,</span>
<span id="cb238-21"><a href="#cb238-21" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.r2 =</span> <span class="cn">TRUE</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<table style="border-collapse:collapse; border:none;">
<caption style="font-weight: bold; text-align:left;">P3 Mixed Effects Models (11-Class) - Province Random Effects</caption>
<tbody><tr>
<th style="border-top: double; text-align:center; font-style:normal; font-weight:bold; padding:0.2cm;  text-align:left; ">&nbsp;</th>
<th colspan="3" style="border-top: double; text-align:center; font-style:normal; font-weight:bold; padding:0.2cm; ">Environmental Information Disclosure</th>
</tr>
<tr>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  text-align:left; ">Predictors</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">p</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col7">p</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col8">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col9">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  0">p</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">(Intercept)</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-74.72</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-79.29&nbsp;–&nbsp;-70.15</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-63.32</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-67.61&nbsp;–&nbsp;-59.03</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-65.16</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-69.65&nbsp;–&nbsp;-60.67</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">after_first_inspection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">6.61</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">6.18&nbsp;–&nbsp;7.04</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">5.92</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">5.35&nbsp;–&nbsp;6.49</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">after_first_inspection:connection_num</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.19</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">0.03&nbsp;–&nbsp;0.35</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>0.020</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">Age</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.25</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.21&nbsp;–&nbsp;0.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.04</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">0.00&nbsp;–&nbsp;0.08</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>0.040</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">connection_num</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.38&nbsp;–&nbsp;-0.20</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-0.23</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.34&nbsp;–&nbsp;-0.11</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">ESG_Rate</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.27</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.24&nbsp;–&nbsp;0.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.26</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.23&nbsp;–&nbsp;0.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.26</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">0.23&nbsp;–&nbsp;0.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">Leverage</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.617</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.573</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.547</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">RegisterCapital_log</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">3.01</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.79&nbsp;–&nbsp;3.23</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.50</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.30&nbsp;–&nbsp;2.70</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">2.58</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">2.36&nbsp;–&nbsp;2.79</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">ROA</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.02</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.18&nbsp;–&nbsp;0.22</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.862</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.04</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.16&nbsp;–&nbsp;0.23</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.711</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.03</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.16&nbsp;–&nbsp;0.23</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.731</td>
</tr>
<tr>
<td colspan="10" style="font-weight:bold; text-align:left; padding-top:.8em;">Random Effects</td>
</tr>

<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">σ<sup>2</sup></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">126.34</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">118.99</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">118.60</td>
</tr>

<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">τ<sub>00</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">12.70 <sub>CITY:PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11.28 <sub>CITY:PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11.38 <sub>CITY:PROVINCE</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">1.45 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">1.51 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">1.58 <sub>PROVINCE</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">ICC</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.10</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.10</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.10</td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">N</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm; border-top:1px solid;">Observations</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10771</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10777</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10771</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">Marginal R<sup>2</sup> / Conditional R<sup>2</sup></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.129 / 0.217</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.179 / 0.258</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.179 / 0.260</td>
</tr>

</tbody></table>

</div>
</div>
<div class="cell">
<div class="sourceCode cell-code" id="cb239"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb239-1"><a href="#cb239-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 中央/地方政治关联的混合效应模型</span></span>
<span id="cb239-2"><a href="#cb239-2" aria-hidden="true" tabindex="-1"></a>p4mix1_11_1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> central_connection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> </span>
<span id="cb239-3"><a href="#cb239-3" aria-hidden="true" tabindex="-1"></a>                  RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY), </span>
<span id="cb239-4"><a href="#cb239-4" aria-hidden="true" tabindex="-1"></a>                  <span class="at">data =</span> dta1_11class)</span>
<span id="cb239-5"><a href="#cb239-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb239-6"><a href="#cb239-6" aria-hidden="true" tabindex="-1"></a>p4mix2_11_1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> central_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span> </span>
<span id="cb239-7"><a href="#cb239-7" aria-hidden="true" tabindex="-1"></a>                  ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span> </span>
<span id="cb239-8"><a href="#cb239-8" aria-hidden="true" tabindex="-1"></a>                  (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY), </span>
<span id="cb239-9"><a href="#cb239-9" aria-hidden="true" tabindex="-1"></a>                  <span class="at">data =</span> dta1_11class)</span>
<span id="cb239-10"><a href="#cb239-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb239-11"><a href="#cb239-11" aria-hidden="true" tabindex="-1"></a>p4mix3_11_1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> local_connection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> </span>
<span id="cb239-12"><a href="#cb239-12" aria-hidden="true" tabindex="-1"></a>                  RegisterCapital_log <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY), </span>
<span id="cb239-13"><a href="#cb239-13" aria-hidden="true" tabindex="-1"></a>                  <span class="at">data =</span> dta1_11class)</span>
<span id="cb239-14"><a href="#cb239-14" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb239-15"><a href="#cb239-15" aria-hidden="true" tabindex="-1"></a>p4mix4_11_1 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> local_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span></span>
<span id="cb239-16"><a href="#cb239-16" aria-hidden="true" tabindex="-1"></a>                  ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb239-17"><a href="#cb239-17" aria-hidden="true" tabindex="-1"></a>                  (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY) ,</span>
<span id="cb239-18"><a href="#cb239-18" aria-hidden="true" tabindex="-1"></a>                  <span class="at">data =</span> dta1_11class)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
</div>
<div class="cell">
<div class="sourceCode cell-code" id="cb240"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb240-1"><a href="#cb240-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 输出结果</span></span>
<span id="cb240-2"><a href="#cb240-2" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb240-3"><a href="#cb240-3" aria-hidden="true" tabindex="-1"></a><span class="fu">tab_model</span>(p4mix1_11_1, p4mix2_11_1, p4mix3_11_1, p4mix4_11_1,</span>
<span id="cb240-4"><a href="#cb240-4" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"P4 Mixed Effects Models (11-Class) - Central vs Local Connections"</span>,</span>
<span id="cb240-5"><a href="#cb240-5" aria-hidden="true" tabindex="-1"></a>          <span class="at">dv.labels =</span> <span class="st">"Environmental Information Disclosure"</span>,</span>
<span id="cb240-6"><a href="#cb240-6" aria-hidden="true" tabindex="-1"></a>          <span class="at">rm.terms =</span> <span class="fu">c</span>(<span class="st">"as.factor"</span>),</span>
<span id="cb240-7"><a href="#cb240-7" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.reflvl =</span> <span class="cn">TRUE</span>,</span>
<span id="cb240-8"><a href="#cb240-8" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.icc =</span> <span class="cn">TRUE</span>,</span>
<span id="cb240-9"><a href="#cb240-9" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.r2 =</span> <span class="cn">TRUE</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<table style="border-collapse:collapse; border:none;">
<caption style="font-weight: bold; text-align:left;">P4 Mixed Effects Models (11-Class) - Central vs Local Connections</caption>
<tbody><tr>
<th style="border-top: double; text-align:center; font-style:normal; font-weight:bold; padding:0.2cm;  text-align:left; ">&nbsp;</th>
<th colspan="3" style="border-top: double; text-align:center; font-style:normal; font-weight:bold; padding:0.2cm; ">Environmental Information Disclosure</th>
</tr>
<tr>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  text-align:left; ">Predictors</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">p</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col7">p</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col8">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col9">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  0">p</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  1">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  2">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  3">p</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">(Intercept)</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-73.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-77.84&nbsp;–&nbsp;-68.74</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-64.39</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-68.86&nbsp;–&nbsp;-59.93</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-73.89</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-78.41&nbsp;–&nbsp;-69.36</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">-65.22</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">-69.65&nbsp;–&nbsp;-60.80</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">after_first_inspection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">6.30</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">5.84&nbsp;–&nbsp;6.76</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">6.19</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">5.65&nbsp;–&nbsp;6.74</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">Age</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.25</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.21&nbsp;–&nbsp;0.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.26</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">0.22&nbsp;–&nbsp;0.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">central_connection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.76</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-1.10&nbsp;–&nbsp;-0.41</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.61</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-1.03&nbsp;–&nbsp;-0.19</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>0.004</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">central_connection:after_first_inspection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">1.08</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.44&nbsp;–&nbsp;1.73</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">ESG_Rate</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.27</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.24&nbsp;–&nbsp;0.30</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.26</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.23&nbsp;–&nbsp;0.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.26</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">0.24&nbsp;–&nbsp;0.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">0.26</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">0.23&nbsp;–&nbsp;0.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">Leverage</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.635</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.584</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.611</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3">0.564</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">local_connection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-0.30</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.40&nbsp;–&nbsp;-0.20</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">-0.26</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">-0.39&nbsp;–&nbsp;-0.12</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">local_connection:after_first_inspection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">0.19</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">0.00&nbsp;–&nbsp;0.37</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>0.047</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">RegisterCapital_log</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.91</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.70&nbsp;–&nbsp;3.13</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.56</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.35&nbsp;–&nbsp;2.77</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">2.97</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">2.75&nbsp;–&nbsp;3.18</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">2.62</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">2.40&nbsp;–&nbsp;2.83</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">ROA</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.02</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.18&nbsp;–&nbsp;0.22</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.858</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.04</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.16&nbsp;–&nbsp;0.23</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.716</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.02</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.18&nbsp;–&nbsp;0.22</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.862</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">0.04</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">-0.16&nbsp;–&nbsp;0.23</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3">0.717</td>
</tr>
<tr>
<td colspan="13" style="font-weight:bold; text-align:left; padding-top:.8em;">Random Effects</td>
</tr>

<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">σ<sup>2</sup></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">126.57</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">118.86</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">126.41</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">118.85</td>
</tr>

<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">τ<sub>00</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">12.92 <sub>CITY:PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11.39 <sub>CITY:PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">12.66 <sub>CITY:PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11.23 <sub>CITY:PROVINCE</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">1.23 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">1.55 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">1.46 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">1.65 <sub>PROVINCE</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">ICC</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.10</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.10</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.10</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.10</td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">N</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm; border-top:1px solid;">Observations</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10771</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10777</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10771</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10777</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">Marginal R<sup>2</sup> / Conditional R<sup>2</sup></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.127 / 0.215</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.179 / 0.260</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.129 / 0.216</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.180 / 0.260</td>
</tr>

</tbody></table>

</div>
</div>
</section>
<section id="包含行业随机效应的混合效应模型" class="level2">
<h2 class="anchored" data-anchor-id="包含行业随机效应的混合效应模型">包含行业随机效应的混合效应模型</h2>
<div class="cell">
<div class="sourceCode cell-code" id="cb241"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb241-1"><a href="#cb241-1" aria-hidden="true" tabindex="-1"></a>p3mix1_slope_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> connection_num <span class="sc">+</span></span>
<span id="cb241-2"><a href="#cb241-2" aria-hidden="true" tabindex="-1"></a>                       ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb241-3"><a href="#cb241-3" aria-hidden="true" tabindex="-1"></a>                       (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY) <span class="sc">+</span> (<span class="dv">0</span> <span class="sc">+</span> connection_num <span class="sc">|</span> industry_type11),</span>
<span id="cb241-4"><a href="#cb241-4" aria-hidden="true" tabindex="-1"></a>                       <span class="at">data =</span> dta1_11class)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output cell-output-stderr">
<pre><code>Warning in checkConv(attr(opt, "derivs"), opt$par, ctrl = control$checkConv, :
Model failed to converge with max|grad| = 0.0108512 (tol = 0.002, component 1)</code></pre>
</div>
<div class="sourceCode cell-code" id="cb243"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb243-1"><a href="#cb243-1" aria-hidden="true" tabindex="-1"></a>p3mix2_slope_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> after_first_inspection <span class="sc">+</span></span>
<span id="cb243-2"><a href="#cb243-2" aria-hidden="true" tabindex="-1"></a>                       ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb243-3"><a href="#cb243-3" aria-hidden="true" tabindex="-1"></a>                       (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY) <span class="sc">+</span> (<span class="dv">0</span> <span class="sc">+</span> connection_num <span class="sc">|</span> industry_type11),</span>
<span id="cb243-4"><a href="#cb243-4" aria-hidden="true" tabindex="-1"></a>                       <span class="at">data =</span> dta1_11class)</span>
<span id="cb243-5"><a href="#cb243-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb243-6"><a href="#cb243-6" aria-hidden="true" tabindex="-1"></a>p3mix3_slope_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> after_first_inspection <span class="sc">*</span> connection_num <span class="sc">+</span></span>
<span id="cb243-7"><a href="#cb243-7" aria-hidden="true" tabindex="-1"></a>                       ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb243-8"><a href="#cb243-8" aria-hidden="true" tabindex="-1"></a>                       (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY) <span class="sc">+</span> (<span class="dv">0</span> <span class="sc">+</span> connection_num <span class="sc">|</span> industry_type11),</span>
<span id="cb243-9"><a href="#cb243-9" aria-hidden="true" tabindex="-1"></a>                       <span class="at">data =</span> dta1_11class)</span>
<span id="cb243-10"><a href="#cb243-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb243-11"><a href="#cb243-11" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb243-12"><a href="#cb243-12" aria-hidden="true" tabindex="-1"></a><span class="co"># 中央/地方政治关联的混合效应模型</span></span>
<span id="cb243-13"><a href="#cb243-13" aria-hidden="true" tabindex="-1"></a>p4mix1_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> central_connection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> </span>
<span id="cb243-14"><a href="#cb243-14" aria-hidden="true" tabindex="-1"></a>                  RegisterCapital_log <span class="sc">+</span>   (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> industry_type11), </span>
<span id="cb243-15"><a href="#cb243-15" aria-hidden="true" tabindex="-1"></a>                  <span class="at">data =</span> dta1_11class)</span>
<span id="cb243-16"><a href="#cb243-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb243-17"><a href="#cb243-17" aria-hidden="true" tabindex="-1"></a>p4mix2_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> central_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span> </span>
<span id="cb243-18"><a href="#cb243-18" aria-hidden="true" tabindex="-1"></a>                  ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>   </span>
<span id="cb243-19"><a href="#cb243-19" aria-hidden="true" tabindex="-1"></a>                  (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY) <span class="sc">+</span> (<span class="dv">0</span> <span class="sc">+</span> central_connection <span class="sc">|</span> industry_type11), </span>
<span id="cb243-20"><a href="#cb243-20" aria-hidden="true" tabindex="-1"></a>                  <span class="at">data =</span> dta1_11class)</span>
<span id="cb243-21"><a href="#cb243-21" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb243-22"><a href="#cb243-22" aria-hidden="true" tabindex="-1"></a>p4mix3_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> local_connection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> </span>
<span id="cb243-23"><a href="#cb243-23" aria-hidden="true" tabindex="-1"></a>                  RegisterCapital_log <span class="sc">+</span>   (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> industry_type11), </span>
<span id="cb243-24"><a href="#cb243-24" aria-hidden="true" tabindex="-1"></a>                  <span class="at">data =</span> dta1_11class)</span>
<span id="cb243-25"><a href="#cb243-25" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb243-26"><a href="#cb243-26" aria-hidden="true" tabindex="-1"></a>p4mix4_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> local_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span></span>
<span id="cb243-27"><a href="#cb243-27" aria-hidden="true" tabindex="-1"></a>                  ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb243-28"><a href="#cb243-28" aria-hidden="true" tabindex="-1"></a>                  (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY) <span class="sc">+</span> (<span class="dv">0</span> <span class="sc">+</span> local_connection <span class="sc">|</span> industry_type11),</span>
<span id="cb243-29"><a href="#cb243-29" aria-hidden="true" tabindex="-1"></a>                  <span class="at">data =</span> dta1_11class)</span>
<span id="cb243-30"><a href="#cb243-30" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb243-31"><a href="#cb243-31" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb243-32"><a href="#cb243-32" aria-hidden="true" tabindex="-1"></a><span class="co"># 输出随机斜率模型结果</span></span>
<span id="cb243-33"><a href="#cb243-33" aria-hidden="true" tabindex="-1"></a><span class="fu">tab_model</span>(p3mix1_slope_11, p3mix2_slope_11, p3mix3_slope_11,</span>
<span id="cb243-34"><a href="#cb243-34" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"Random Slope Models (11-Class) - Industry-specific Interaction Effects"</span>,</span>
<span id="cb243-35"><a href="#cb243-35" aria-hidden="true" tabindex="-1"></a>          <span class="at">dv.labels =</span> <span class="st">"Environmental Information Disclosure"</span>,</span>
<span id="cb243-36"><a href="#cb243-36" aria-hidden="true" tabindex="-1"></a>          <span class="at">rm.terms =</span> <span class="fu">c</span>(<span class="st">"as.factor"</span>),</span>
<span id="cb243-37"><a href="#cb243-37" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.reflvl =</span> <span class="cn">TRUE</span>,</span>
<span id="cb243-38"><a href="#cb243-38" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.icc =</span> <span class="cn">TRUE</span>,</span>
<span id="cb243-39"><a href="#cb243-39" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.r2 =</span> <span class="cn">TRUE</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<table style="border-collapse:collapse; border:none;">
<caption style="font-weight: bold; text-align:left;">Random Slope Models (11-Class) - Industry-specific Interaction Effects</caption>
<tbody><tr>
<th style="border-top: double; text-align:center; font-style:normal; font-weight:bold; padding:0.2cm;  text-align:left; ">&nbsp;</th>
<th colspan="3" style="border-top: double; text-align:center; font-style:normal; font-weight:bold; padding:0.2cm; ">Environmental Information Disclosure</th>
</tr>
<tr>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  text-align:left; ">Predictors</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">p</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col7">p</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col8">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col9">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  0">p</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">(Intercept)</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-79.49</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-84.11&nbsp;–&nbsp;-74.87</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-69.16</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-73.71&nbsp;–&nbsp;-64.61</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-69.05</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-73.60&nbsp;–&nbsp;-64.49</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">after_first_inspection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">6.09</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">5.62&nbsp;–&nbsp;6.55</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">5.82</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">5.26&nbsp;–&nbsp;6.39</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">after_first_inspection:connection_num</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.14</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.03&nbsp;–&nbsp;0.30</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.118</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">Age</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.27</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.24&nbsp;–&nbsp;0.31</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.06</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.02&nbsp;–&nbsp;0.10</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>0.002</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.06</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">0.02&nbsp;–&nbsp;0.10</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>0.003</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">connection_num</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.07</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.60&nbsp;–&nbsp;0.46</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.806</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-0.02</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.52&nbsp;–&nbsp;0.48</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.933</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">ESG_Rate</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.26</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.23&nbsp;–&nbsp;0.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.26</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.23&nbsp;–&nbsp;0.28</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.26</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">0.23&nbsp;–&nbsp;0.28</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">Leverage</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.751</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.674</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.670</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">RegisterCapital_log</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">3.20</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.97&nbsp;–&nbsp;3.42</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.73</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.52&nbsp;–&nbsp;2.95</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">2.73</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">2.52&nbsp;–&nbsp;2.95</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">ROA</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.01</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.19&nbsp;–&nbsp;0.21</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.934</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.03</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.17&nbsp;–&nbsp;0.22</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.790</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.03</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.17&nbsp;–&nbsp;0.22</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.793</td>
</tr>
<tr>
<td colspan="10" style="font-weight:bold; text-align:left; padding-top:.8em;">Random Effects</td>
</tr>

<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">σ<sup>2</sup></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">121.88</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">114.96</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">114.94</td>
</tr>

<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">τ<sub>00</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">13.04 <sub>CITY:PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11.39 <sub>CITY:PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11.43 <sub>CITY:PROVINCE</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">1.94 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">1.89 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">1.89 <sub>PROVINCE</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">τ<sub>11</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.77 <sub>industry_type11.connection_num</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.61 <sub>industry_type11.connection_num</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.67 <sub>industry_type11.connection_num</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">ρ<sub>01</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">ρ<sub>01</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">ICC</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.16</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.10</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.15</td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">N</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11 <sub>industry_type11</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11 <sub>industry_type11</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11 <sub>industry_type11</sub></td>
</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm; border-top:1px solid;">Observations</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10771</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10771</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10771</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">Marginal R<sup>2</sup> / Conditional R<sup>2</sup></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.139 / 0.278</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.189 / 0.273</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.181 / 0.306</td>
</tr>

</tbody></table>

</div>
<div class="sourceCode cell-code" id="cb244"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb244-1"><a href="#cb244-1" aria-hidden="true" tabindex="-1"></a><span class="fu">tab_model</span>(p4mix1_11, p4mix2_11, p4mix3_11, p4mix4_11,</span>
<span id="cb244-2"><a href="#cb244-2" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"P4 Mixed Effects Models (11-Class) - Central vs Local Connections"</span>,</span>
<span id="cb244-3"><a href="#cb244-3" aria-hidden="true" tabindex="-1"></a>          <span class="at">dv.labels =</span> <span class="st">"Environmental Information Disclosure"</span>,</span>
<span id="cb244-4"><a href="#cb244-4" aria-hidden="true" tabindex="-1"></a>          <span class="at">rm.terms =</span> <span class="fu">c</span>(<span class="st">"as.factor"</span>),</span>
<span id="cb244-5"><a href="#cb244-5" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.reflvl =</span> <span class="cn">TRUE</span>,</span>
<span id="cb244-6"><a href="#cb244-6" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.icc =</span> <span class="cn">TRUE</span>,</span>
<span id="cb244-7"><a href="#cb244-7" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.r2 =</span> <span class="cn">TRUE</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<table style="border-collapse:collapse; border:none;">
<caption style="font-weight: bold; text-align:left;">P4 Mixed Effects Models (11-Class) - Central vs Local Connections</caption>
<tbody><tr>
<th style="border-top: double; text-align:center; font-style:normal; font-weight:bold; padding:0.2cm;  text-align:left; ">&nbsp;</th>
<th colspan="3" style="border-top: double; text-align:center; font-style:normal; font-weight:bold; padding:0.2cm; ">Environmental Information Disclosure</th>
</tr>
<tr>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  text-align:left; ">Predictors</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">p</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col7">p</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col8">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col9">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  0">p</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  1">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  2">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  3">p</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">(Intercept)</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-82.43</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-87.56&nbsp;–&nbsp;-77.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-66.20</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-70.76&nbsp;–&nbsp;-61.64</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-82.07</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-87.16&nbsp;–&nbsp;-76.98</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">-68.66</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">-73.14&nbsp;–&nbsp;-64.18</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">after_first_inspection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">6.37</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">5.91&nbsp;–&nbsp;6.83</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">6.15</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">5.61&nbsp;–&nbsp;6.69</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">Age</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.30</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.26&nbsp;–&nbsp;0.34</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.31</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">0.27&nbsp;–&nbsp;0.34</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">central_connection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.46</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.80&nbsp;–&nbsp;-0.12</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>0.008</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.51</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-1.90&nbsp;–&nbsp;0.89</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.476</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">central_connection:after_first_inspection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.62</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.11&nbsp;–&nbsp;1.34</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.094</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">ESG_Rate</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.25</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.23&nbsp;–&nbsp;0.28</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.26</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.23&nbsp;–&nbsp;0.28</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.25</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">0.22&nbsp;–&nbsp;0.28</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">0.26</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">0.23&nbsp;–&nbsp;0.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">Leverage</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.843</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.594</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.823</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3">0.714</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">local_connection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-0.13</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.24&nbsp;–&nbsp;-0.03</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>0.011</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">-0.02</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">-0.59&nbsp;–&nbsp;0.54</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3">0.935</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">local_connection:after_first_inspection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">0.14</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">-0.05&nbsp;–&nbsp;0.33</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3">0.145</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">RegisterCapital_log</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">3.27</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">3.05&nbsp;–&nbsp;3.49</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.66</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.44&nbsp;–&nbsp;2.87</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">3.26</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">3.04&nbsp;–&nbsp;3.48</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">2.76</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">2.54&nbsp;–&nbsp;2.97</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">ROA</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.01</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.20&nbsp;–&nbsp;0.19</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.959</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.03</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.16&nbsp;–&nbsp;0.23</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.728</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.20&nbsp;–&nbsp;0.19</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.962</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">0.03</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">-0.16&nbsp;–&nbsp;0.22</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3">0.768</td>
</tr>
<tr>
<td colspan="13" style="font-weight:bold; text-align:left; padding-top:.8em;">Random Effects</td>
</tr>

<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">σ<sup>2</sup></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">119.14</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">117.67</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">119.15</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">115.26</td>
</tr>

<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">τ<sub>00</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">13.03 <sub>CITY:PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11.41 <sub>CITY:PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">13.02 <sub>CITY:PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11.13 <sub>CITY:PROVINCE</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">2.74 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">1.76 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">2.84 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">1.85 <sub>PROVINCE</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">14.40 <sub>industry_type11</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">14.26 <sub>industry_type11</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">τ<sub>11</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">4.75 <sub>industry_type11.central_connection</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.85 <sub>industry_type11.local_connection</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">ρ<sub>01</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">ρ<sub>01</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">&nbsp;</td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">ICC</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.20</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.12</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.20</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.15</td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">N</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11 <sub>industry_type11</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11 <sub>industry_type11</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11 <sub>industry_type11</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11 <sub>industry_type11</sub></td>
</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm; border-top:1px solid;">Observations</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10771</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10777</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10771</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10777</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">Marginal R<sup>2</sup> / Conditional R<sup>2</sup></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.141 / 0.314</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.179 / 0.275</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.141 / 0.314</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.182 / 0.303</td>
</tr>

</tbody></table>

</div>
</div>
</section>
<section id="包含行业固定效应的混合效应模型" class="level2">
<h2 class="anchored" data-anchor-id="包含行业固定效应的混合效应模型">包含行业固定效应的混合效应模型</h2>
<div class="cell">
<div class="sourceCode cell-code" id="cb245"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb245-1"><a href="#cb245-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 包含行业固定效应的混合效应模型</span></span>
<span id="cb245-2"><a href="#cb245-2" aria-hidden="true" tabindex="-1"></a>p3mix1_fixed_industry_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> connection_num <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span></span>
<span id="cb245-3"><a href="#cb245-3" aria-hidden="true" tabindex="-1"></a>                                RegisterCapital_log <span class="sc">+</span>   <span class="fu">as.factor</span>(industry_type11) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb245-4"><a href="#cb245-4" aria-hidden="true" tabindex="-1"></a>                                <span class="at">data =</span> dta1_11class)</span>
<span id="cb245-5"><a href="#cb245-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb245-6"><a href="#cb245-6" aria-hidden="true" tabindex="-1"></a>p3mix2_fixed_industry_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> after_first_inspection <span class="sc">+</span> ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span></span>
<span id="cb245-7"><a href="#cb245-7" aria-hidden="true" tabindex="-1"></a>                                RegisterCapital_log <span class="sc">+</span>   <span class="fu">as.factor</span>(industry_type11) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb245-8"><a href="#cb245-8" aria-hidden="true" tabindex="-1"></a>                                <span class="at">data =</span> dta1_11class)</span>
<span id="cb245-9"><a href="#cb245-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb245-10"><a href="#cb245-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb245-11"><a href="#cb245-11" aria-hidden="true" tabindex="-1"></a>p3mix3_fixed_industry_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> Age <span class="sc">+</span> after_first_inspection <span class="sc">*</span> connection_num <span class="sc">+</span></span>
<span id="cb245-12"><a href="#cb245-12" aria-hidden="true" tabindex="-1"></a>                                   ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb245-13"><a href="#cb245-13" aria-hidden="true" tabindex="-1"></a>                                   <span class="fu">as.factor</span>(industry_type11) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb245-14"><a href="#cb245-14" aria-hidden="true" tabindex="-1"></a>                                   <span class="at">data =</span> dta1_11class)</span>
<span id="cb245-15"><a href="#cb245-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb245-16"><a href="#cb245-16" aria-hidden="true" tabindex="-1"></a>p4mix1_fixed_central_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> central_connection <span class="sc">+</span></span>
<span id="cb245-17"><a href="#cb245-17" aria-hidden="true" tabindex="-1"></a>                               ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb245-18"><a href="#cb245-18" aria-hidden="true" tabindex="-1"></a>                               <span class="fu">as.factor</span>(industry_type11) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb245-19"><a href="#cb245-19" aria-hidden="true" tabindex="-1"></a>                               <span class="at">data =</span> dta1_11class)</span>
<span id="cb245-20"><a href="#cb245-20" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb245-21"><a href="#cb245-21" aria-hidden="true" tabindex="-1"></a>p4mix2_fixed_central_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> central_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span></span>
<span id="cb245-22"><a href="#cb245-22" aria-hidden="true" tabindex="-1"></a>                               ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb245-23"><a href="#cb245-23" aria-hidden="true" tabindex="-1"></a>                               <span class="fu">as.factor</span>(industry_type11) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb245-24"><a href="#cb245-24" aria-hidden="true" tabindex="-1"></a>                               <span class="at">data =</span> dta1_11class)</span>
<span id="cb245-25"><a href="#cb245-25" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb245-26"><a href="#cb245-26" aria-hidden="true" tabindex="-1"></a>p4mix1_fixed_local_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> local_connection <span class="sc">+</span></span>
<span id="cb245-27"><a href="#cb245-27" aria-hidden="true" tabindex="-1"></a>                             ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb245-28"><a href="#cb245-28" aria-hidden="true" tabindex="-1"></a>                             <span class="fu">as.factor</span>(industry_type11) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb245-29"><a href="#cb245-29" aria-hidden="true" tabindex="-1"></a>                             <span class="at">data =</span> dta1_11class)</span>
<span id="cb245-30"><a href="#cb245-30" aria-hidden="true" tabindex="-1"></a>p4mix2_fixed_local_11 <span class="ot">&lt;-</span> <span class="fu">lmer</span>(Environmental_Information_Disclosure <span class="sc">~</span> local_connection <span class="sc">*</span> after_first_inspection <span class="sc">+</span></span>
<span id="cb245-31"><a href="#cb245-31" aria-hidden="true" tabindex="-1"></a>                             ESG_Rate <span class="sc">+</span> ROA <span class="sc">+</span> Leverage <span class="sc">+</span> RegisterCapital_log <span class="sc">+</span>  </span>
<span id="cb245-32"><a href="#cb245-32" aria-hidden="true" tabindex="-1"></a>                             <span class="fu">as.factor</span>(industry_type11) <span class="sc">+</span> (<span class="dv">1</span> <span class="sc">|</span> PROVINCE<span class="sc">/</span>CITY),</span>
<span id="cb245-33"><a href="#cb245-33" aria-hidden="true" tabindex="-1"></a>                             <span class="at">data =</span> dta1_11class)</span>
<span id="cb245-34"><a href="#cb245-34" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb245-35"><a href="#cb245-35" aria-hidden="true" tabindex="-1"></a><span class="co"># 输出结果</span></span>
<span id="cb245-36"><a href="#cb245-36" aria-hidden="true" tabindex="-1"></a><span class="fu">tab_model</span>(p3mix1_fixed_industry_11, p3mix2_fixed_industry_11, p3mix3_fixed_industry_11,</span>
<span id="cb245-37"><a href="#cb245-37" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"Mixed Effects Models with Industry Fixed Effects (11-Class) - P3 Models"</span>,</span>
<span id="cb245-38"><a href="#cb245-38" aria-hidden="true" tabindex="-1"></a>          <span class="at">dv.labels =</span> <span class="st">"Environmental Information Disclosure"</span>,</span>
<span id="cb245-39"><a href="#cb245-39" aria-hidden="true" tabindex="-1"></a>          <span class="at">rm.terms =</span> <span class="fu">c</span>(<span class="st">"as.factor"</span>),</span>
<span id="cb245-40"><a href="#cb245-40" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.reflvl =</span> <span class="cn">TRUE</span>,</span>
<span id="cb245-41"><a href="#cb245-41" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.icc =</span> <span class="cn">TRUE</span>,</span>
<span id="cb245-42"><a href="#cb245-42" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.r2 =</span> <span class="cn">TRUE</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<table style="border-collapse:collapse; border:none;">
<caption style="font-weight: bold; text-align:left;">Mixed Effects Models with Industry Fixed Effects (11-Class) - P3 Models</caption>
<tbody><tr>
<th style="border-top: double; text-align:center; font-style:normal; font-weight:bold; padding:0.2cm;  text-align:left; ">&nbsp;</th>
<th colspan="3" style="border-top: double; text-align:center; font-style:normal; font-weight:bold; padding:0.2cm; ">Environmental Information Disclosure</th>
</tr>
<tr>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  text-align:left; ">Predictors</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">p</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col7">p</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col8">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col9">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  0">p</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">(Intercept)</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-84.92</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-89.65&nbsp;–&nbsp;-80.19</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-74.46</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-79.02&nbsp;–&nbsp;-69.90</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-74.89</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-79.55&nbsp;–&nbsp;-70.23</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">after_first_inspection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">5.92</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">5.46&nbsp;–&nbsp;6.38</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">5.58</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">5.02&nbsp;–&nbsp;6.15</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">after_first_inspection:connection_num</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.16</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.00&nbsp;–&nbsp;0.32</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.054</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">Age</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.31</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.27&nbsp;–&nbsp;0.34</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.09</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.05&nbsp;–&nbsp;0.14</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.09</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">0.05&nbsp;–&nbsp;0.13</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Consumer Discretionary</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">1.63</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.30&nbsp;–&nbsp;2.96</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>0.016</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.44</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">1.14&nbsp;–&nbsp;3.73</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">2.45</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">1.15&nbsp;–&nbsp;3.74</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Consumer Staples</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">3.53</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.01&nbsp;–&nbsp;5.05</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">3.86</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.39&nbsp;–&nbsp;5.34</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">3.91</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">2.43&nbsp;–&nbsp;5.38</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Energy</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">6.14</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">4.29&nbsp;–&nbsp;8.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">7.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">5.20&nbsp;–&nbsp;8.79</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">7.02</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">5.22&nbsp;–&nbsp;8.82</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Financials</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-3.42</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-5.09&nbsp;–&nbsp;-1.75</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-1.52</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-3.13&nbsp;–&nbsp;0.10</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.066</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-1.21</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-2.86&nbsp;–&nbsp;0.44</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.150</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Health Care</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">8.12</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">6.69&nbsp;–&nbsp;9.56</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">8.62</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">7.23&nbsp;–&nbsp;10.01</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">8.65</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">7.26&nbsp;–&nbsp;10.05</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Industrials</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.27</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">1.01&nbsp;–&nbsp;3.52</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.73</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">1.51&nbsp;–&nbsp;3.95</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">2.75</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">1.53&nbsp;–&nbsp;3.97</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Information Technology</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">3.28</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">1.95&nbsp;–&nbsp;4.60</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">3.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.01&nbsp;–&nbsp;4.58</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">3.32</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">2.03&nbsp;–&nbsp;4.60</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Materials</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">6.14</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">4.84&nbsp;–&nbsp;7.44</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">6.69</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">5.43&nbsp;–&nbsp;7.96</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">6.71</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">5.45&nbsp;–&nbsp;7.98</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Real Estate</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-3.97</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-5.47&nbsp;–&nbsp;-2.48</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-2.23</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-3.69&nbsp;–&nbsp;-0.77</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>0.003</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-2.22</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-3.68&nbsp;–&nbsp;-0.76</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>0.003</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Utilities</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">1.23</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.30&nbsp;–&nbsp;2.76</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.115</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.38</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.90&nbsp;–&nbsp;3.87</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>0.002</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">2.40</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">0.92&nbsp;–&nbsp;3.89</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>0.002</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">connection_num</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.13</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.23&nbsp;–&nbsp;-0.04</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>0.004</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-0.10</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.22&nbsp;–&nbsp;0.01</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.079</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">ESG_Rate</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.25</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.22&nbsp;–&nbsp;0.28</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.25</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.22&nbsp;–&nbsp;0.28</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.25</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">0.22&nbsp;–&nbsp;0.28</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">Leverage</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.827</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.748</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.743</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">RegisterCapital_log</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">3.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">3.07&nbsp;–&nbsp;3.51</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.78</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.57&nbsp;–&nbsp;2.99</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">2.81</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">2.60&nbsp;–&nbsp;3.03</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">ROA</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.01</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.20&nbsp;–&nbsp;0.19</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.959</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.02</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.17&nbsp;–&nbsp;0.21</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.862</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.02</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.18&nbsp;–&nbsp;0.21</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.865</td>
</tr>
<tr>
<td colspan="10" style="font-weight:bold; text-align:left; padding-top:.8em;">Random Effects</td>
</tr>

<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">σ<sup>2</sup></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">119.11</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">112.62</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">112.58</td>
</tr>

<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">τ<sub>00</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">13.11 <sub>CITY:PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11.68 <sub>CITY:PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11.76 <sub>CITY:PROVINCE</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">2.89 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">2.65 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">2.67 <sub>PROVINCE</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">ICC</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.12</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.11</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.11</td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">N</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm; border-top:1px solid;">Observations</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10771</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10771</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10771</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">Marginal R<sup>2</sup> / Conditional R<sup>2</sup></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.180 / 0.277</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.220 / 0.308</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.221 / 0.309</td>
</tr>

</tbody></table>

</div>
<div class="sourceCode cell-code" id="cb246"><pre class="sourceCode r code-with-copy"><code class="sourceCode r"><span id="cb246-1"><a href="#cb246-1" aria-hidden="true" tabindex="-1"></a><span class="fu">tab_model</span>(p4mix1_fixed_central_11, p4mix2_fixed_central_11, p4mix1_fixed_local_11, p4mix2_fixed_local_11,</span>
<span id="cb246-2"><a href="#cb246-2" aria-hidden="true" tabindex="-1"></a>          <span class="at">title =</span> <span class="st">"Mixed Effects Models with Industry Fixed Effects (11-Class) - P4 Models"</span>,</span>
<span id="cb246-3"><a href="#cb246-3" aria-hidden="true" tabindex="-1"></a>          <span class="at">dv.labels =</span> <span class="st">"Environmental Information Disclosure"</span>,</span>
<span id="cb246-4"><a href="#cb246-4" aria-hidden="true" tabindex="-1"></a>          <span class="at">rm.terms =</span> <span class="fu">c</span>(<span class="st">"as.factor"</span>),</span>
<span id="cb246-5"><a href="#cb246-5" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.reflvl =</span> <span class="cn">TRUE</span>,</span>
<span id="cb246-6"><a href="#cb246-6" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.icc =</span> <span class="cn">TRUE</span>,</span>
<span id="cb246-7"><a href="#cb246-7" aria-hidden="true" tabindex="-1"></a>          <span class="at">show.r2 =</span> <span class="cn">TRUE</span>)</span></code><button title="Copy to Clipboard" class="code-copy-button"><i class="bi"></i></button></pre></div>
<div class="cell-output-display">
<table style="border-collapse:collapse; border:none;">
<caption style="font-weight: bold; text-align:left;">Mixed Effects Models with Industry Fixed Effects (11-Class) - P4 Models</caption>
<tbody><tr>
<th style="border-top: double; text-align:center; font-style:normal; font-weight:bold; padding:0.2cm;  text-align:left; ">&nbsp;</th>
<th colspan="3" style="border-top: double; text-align:center; font-style:normal; font-weight:bold; padding:0.2cm; ">Environmental Information Disclosure</th>
</tr>
<tr>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  text-align:left; ">Predictors</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">p</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  ">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col7">p</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col8">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  col9">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  0">p</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  1">Estimates</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  2">CI</td>
<td style=" text-align:center; border-bottom:1px solid; font-style:italic; font-weight:normal;  3">p</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">(Intercept)</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-86.01</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-90.79&nbsp;–&nbsp;-81.22</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-74.67</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-79.35&nbsp;–&nbsp;-70.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-85.05</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-89.79&nbsp;–&nbsp;-80.32</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">-74.72</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">-79.33&nbsp;–&nbsp;-70.11</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">after_first_inspection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">6.12</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">5.67&nbsp;–&nbsp;6.57</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">6.09</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">5.56&nbsp;–&nbsp;6.63</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Consumer Discretionary</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.13</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.79&nbsp;–&nbsp;3.47</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>0.002</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.65</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">1.36&nbsp;–&nbsp;3.95</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">2.03</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">0.68&nbsp;–&nbsp;3.38</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>0.003</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">2.58</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">1.29&nbsp;–&nbsp;3.88</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Consumer Staples</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">3.85</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.31&nbsp;–&nbsp;5.38</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">4.03</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.56&nbsp;–&nbsp;5.51</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">3.69</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">2.15&nbsp;–&nbsp;5.23</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">3.95</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">2.47&nbsp;–&nbsp;5.43</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Energy</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">5.92</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">4.05&nbsp;–&nbsp;7.78</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">7.03</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">5.23&nbsp;–&nbsp;8.83</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">5.93</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">4.06&nbsp;–&nbsp;7.80</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">7.01</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">5.21&nbsp;–&nbsp;8.81</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Financials</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-2.88</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-4.56&nbsp;–&nbsp;-1.19</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.91</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-2.55&nbsp;–&nbsp;0.73</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.277</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-2.99</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-4.68&nbsp;–&nbsp;-1.31</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">-1.02</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">-2.65&nbsp;–&nbsp;0.62</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3">0.224</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Health Care</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">8.74</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">7.30&nbsp;–&nbsp;10.19</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">8.86</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">7.47&nbsp;–&nbsp;10.25</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">8.62</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">7.17&nbsp;–&nbsp;10.06</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">8.80</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">7.40&nbsp;–&nbsp;10.19</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Industrials</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.30</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">1.03&nbsp;–&nbsp;3.57</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.84</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">1.62&nbsp;–&nbsp;4.06</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">2.23</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">0.96&nbsp;–&nbsp;3.50</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">2.79</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">1.57&nbsp;–&nbsp;4.01</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Information Technology</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">3.53</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.20&nbsp;–&nbsp;4.87</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">3.38</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.09&nbsp;–&nbsp;4.66</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">3.32</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">1.98&nbsp;–&nbsp;4.66</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">3.30</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">2.01&nbsp;–&nbsp;4.59</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Materials</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">6.33</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">5.02&nbsp;–&nbsp;7.65</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">6.82</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">5.56&nbsp;–&nbsp;8.09</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">6.23</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">4.91&nbsp;–&nbsp;7.55</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">6.75</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">5.49&nbsp;–&nbsp;8.02</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Real Estate</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-2.53</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-4.03&nbsp;–&nbsp;-1.03</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-1.76</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-3.20&nbsp;–&nbsp;-0.32</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>0.017</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-2.56</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-4.07&nbsp;–&nbsp;-1.06</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">-1.76</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">-3.21&nbsp;–&nbsp;-0.31</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>0.017</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">as.factor(industry_type11)Utilities</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">1.88</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.34&nbsp;–&nbsp;3.43</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>0.017</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.67</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">1.18&nbsp;–&nbsp;4.16</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">1.87</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">0.32&nbsp;–&nbsp;3.41</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>0.018</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">2.63</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">1.14&nbsp;–&nbsp;4.12</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">central_connection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.74</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-1.08&nbsp;–&nbsp;-0.40</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.42</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.83&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.052</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">central_connection:after_first_inspection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.99</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.36&nbsp;–&nbsp;1.63</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>0.002</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">ESG_Rate</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.27</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.24&nbsp;–&nbsp;0.30</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.25</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.23&nbsp;–&nbsp;0.28</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.27</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">0.24&nbsp;–&nbsp;0.29</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">0.25</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">0.23&nbsp;–&nbsp;0.28</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">Leverage</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.957</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.801</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.983</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">-0.00&nbsp;–&nbsp;0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3">0.786</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">local_connection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">-0.18</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.28&nbsp;–&nbsp;-0.07</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">-0.12</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">-0.25&nbsp;–&nbsp;0.02</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3">0.088</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">local_connection:after_first_inspection</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">0.14</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">-0.04&nbsp;–&nbsp;0.32</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3">0.129</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">RegisterCapital_log</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">3.55</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">3.33&nbsp;–&nbsp;3.76</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  "><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.86</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">2.64&nbsp;–&nbsp;3.07</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">3.52</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">3.30&nbsp;–&nbsp;3.74</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0"><strong>&lt;0.001</strong></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">2.86</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">2.65&nbsp;–&nbsp;3.08</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3"><strong>&lt;0.001</strong></td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; ">ROA</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.20&nbsp;–&nbsp;0.20</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.999</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">0.02</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  ">-0.17&nbsp;–&nbsp;0.21</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col7">0.836</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col8">0.00</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  col9">-0.20&nbsp;–&nbsp;0.20</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  0">0.993</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  1">0.02</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  2">-0.17&nbsp;–&nbsp;0.21</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:center;  3">0.833</td>
</tr>
<tr>
<td colspan="13" style="font-weight:bold; text-align:left; padding-top:.8em;">Random Effects</td>
</tr>

<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">σ<sup>2</sup></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">122.15</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">112.93</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">122.23</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">113.01</td>
</tr>

<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">τ<sub>00</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11.99 <sub>CITY:PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11.52 <sub>CITY:PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11.94 <sub>CITY:PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">11.48 <sub>CITY:PROVINCE</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">2.47 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">2.70 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">2.59 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">2.69 <sub>PROVINCE</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">ICC</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.11</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.11</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.11</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.11</td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">N</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">252 <sub>CITY</sub></td>

</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;"></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">32 <sub>PROVINCE</sub></td>
</tr><tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm; border-top:1px solid;">Observations</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10777</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10777</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10777</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left; border-top:1px solid;" colspan="3">10777</td>
</tr>
<tr>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; text-align:left; padding-top:0.1cm; padding-bottom:0.1cm;">Marginal R<sup>2</sup> / Conditional R<sup>2</sup></td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.165 / 0.253</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.221 / 0.308</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.165 / 0.253</td>
<td style=" padding:0.2cm; text-align:left; vertical-align:top; padding-top:0.1cm; padding-bottom:0.1cm; text-align:left;" colspan="3">0.221 / 0.307</td>
</tr>

</tbody></table>

</div>
</div>
</section>
</section>

</main>
<!-- /main column -->
<script id="quarto-html-after-body" type="application/javascript">
window.document.addEventListener("DOMContentLoaded", function (event) {
  const toggleBodyColorMode = (bsSheetEl) => {
    const mode = bsSheetEl.getAttribute("data-mode");
    const bodyEl = window.document.querySelector("body");
    if (mode === "dark") {
      bodyEl.classList.add("quarto-dark");
      bodyEl.classList.remove("quarto-light");
    } else {
      bodyEl.classList.add("quarto-light");
      bodyEl.classList.remove("quarto-dark");
    }
  }
  const toggleBodyColorPrimary = () => {
    const bsSheetEl = window.document.querySelector("link#quarto-bootstrap");
    if (bsSheetEl) {
      toggleBodyColorMode(bsSheetEl);
    }
  }
  toggleBodyColorPrimary();  
  const icon = "";
  const anchorJS = new window.AnchorJS();
  anchorJS.options = {
    placement: 'right',
    icon: icon
  };
  anchorJS.add('.anchored');
  const isCodeAnnotation = (el) => {
    for (const clz of el.classList) {
      if (clz.startsWith('code-annotation-')) {                     
        return true;
      }
    }
    return false;
  }
  const onCopySuccess = function(e) {
    // button target
    const button = e.trigger;
    // don't keep focus
    button.blur();
    // flash "checked"
    button.classList.add('code-copy-button-checked');
    var currentTitle = button.getAttribute("title");
    button.setAttribute("title", "Copied!");
    let tooltip;
    if (window.bootstrap) {
      button.setAttribute("data-bs-toggle", "tooltip");
      button.setAttribute("data-bs-placement", "left");
      button.setAttribute("data-bs-title", "Copied!");
      tooltip = new bootstrap.Tooltip(button, 
        { trigger: "manual", 
          customClass: "code-copy-button-tooltip",
          offset: [0, -8]});
      tooltip.show();    
    }
    setTimeout(function() {
      if (tooltip) {
        tooltip.hide();
        button.removeAttribute("data-bs-title");
        button.removeAttribute("data-bs-toggle");
        button.removeAttribute("data-bs-placement");
      }
      button.setAttribute("title", currentTitle);
      button.classList.remove('code-copy-button-checked');
    }, 1000);
    // clear code selection
    e.clearSelection();
  }
  const getTextToCopy = function(trigger) {
      const codeEl = trigger.previousElementSibling.cloneNode(true);
      for (const childEl of codeEl.children) {
        if (isCodeAnnotation(childEl)) {
          childEl.remove();
        }
      }
      return codeEl.innerText;
  }
  const clipboard = new window.ClipboardJS('.code-copy-button:not([data-in-quarto-modal])', {
    text: getTextToCopy
  });
  clipboard.on('success', onCopySuccess);
  if (window.document.getElementById('quarto-embedded-source-code-modal')) {
    const clipboardModal = new window.ClipboardJS('.code-copy-button[data-in-quarto-modal]', {
      text: getTextToCopy,
      container: window.document.getElementById('quarto-embedded-source-code-modal')
    });
    clipboardModal.on('success', onCopySuccess);
  }
    var localhostRegex = new RegExp(/^(?:http|https):\/\/localhost\:?[0-9]*\//);
    var mailtoRegex = new RegExp(/^mailto:/);
      var filterRegex = new RegExp('/' + window.location.host + '/');
    var isInternal = (href) => {
        return filterRegex.test(href) || localhostRegex.test(href) || mailtoRegex.test(href);
    }
    // Inspect non-navigation links and adorn them if external
 	var links = window.document.querySelectorAll('a[href]:not(.nav-link):not(.navbar-brand):not(.toc-action):not(.sidebar-link):not(.sidebar-item-toggle):not(.pagination-link):not(.no-external):not([aria-hidden]):not(.dropdown-item):not(.quarto-navigation-tool):not(.about-link)');
    for (var i=0; i<links.length; i++) {
      const link = links[i];
      if (!isInternal(link.href)) {
        // undo the damage that might have been done by quarto-nav.js in the case of
        // links that we want to consider external
        if (link.dataset.originalHref !== undefined) {
          link.href = link.dataset.originalHref;
        }
      }
    }
  function tippyHover(el, contentFn, onTriggerFn, onUntriggerFn) {
    const config = {
      allowHTML: true,
      maxWidth: 500,
      delay: 100,
      arrow: false,
      appendTo: function(el) {
          return el.parentElement;
      },
      interactive: true,
      interactiveBorder: 10,
      theme: 'quarto',
      placement: 'bottom-start',
    };
    if (contentFn) {
      config.content = contentFn;
    }
    if (onTriggerFn) {
      config.onTrigger = onTriggerFn;
    }
    if (onUntriggerFn) {
      config.onUntrigger = onUntriggerFn;
    }
    window.tippy(el, config); 
  }
  const noterefs = window.document.querySelectorAll('a[role="doc-noteref"]');
  for (var i=0; i<noterefs.length; i++) {
    const ref = noterefs[i];
    tippyHover(ref, function() {
      // use id or data attribute instead here
      let href = ref.getAttribute('data-footnote-href') || ref.getAttribute('href');
      try { href = new URL(href).hash; } catch {}
      const id = href.replace(/^#\/?/, "");
      const note = window.document.getElementById(id);
      if (note) {
        return note.innerHTML;
      } else {
        return "";
      }
    });
  }
  const xrefs = window.document.querySelectorAll('a.quarto-xref');
  const processXRef = (id, note) => {
    // Strip column container classes
    const stripColumnClz = (el) => {
      el.classList.remove("page-full", "page-columns");
      if (el.children) {
        for (const child of el.children) {
          stripColumnClz(child);
        }
      }
    }
    stripColumnClz(note)
    if (id === null || id.startsWith('sec-')) {
      // Special case sections, only their first couple elements
      const container = document.createElement("div");
      if (note.children && note.children.length > 2) {
        container.appendChild(note.children[0].cloneNode(true));
        for (let i = 1; i < note.children.length; i++) {
          const child = note.children[i];
          if (child.tagName === "P" && child.innerText === "") {
            continue;
          } else {
            container.appendChild(child.cloneNode(true));
            break;
          }
        }
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(container);
        }
        return container.innerHTML
      } else {
        if (window.Quarto?.typesetMath) {
          window.Quarto.typesetMath(note);
        }
        return note.innerHTML;
      }
    } else {
      // Remove any anchor links if they are present
      const anchorLink = note.querySelector('a.anchorjs-link');
      if (anchorLink) {
        anchorLink.remove();
      }
      if (window.Quarto?.typesetMath) {
        window.Quarto.typesetMath(note);
      }
      if (note.classList.contains("callout")) {
        return note.outerHTML;
      } else {
        return note.innerHTML;
      }
    }
  }
  for (var i=0; i<xrefs.length; i++) {
    const xref = xrefs[i];
    tippyHover(xref, undefined, function(instance) {
      instance.disable();
      let url = xref.getAttribute('href');
      let hash = undefined; 
      if (url.startsWith('#')) {
        hash = url;
      } else {
        try { hash = new URL(url).hash; } catch {}
      }
      if (hash) {
        const id = hash.replace(/^#\/?/, "");
        const note = window.document.getElementById(id);
        if (note !== null) {
          try {
            const html = processXRef(id, note.cloneNode(true));
            instance.setContent(html);
          } finally {
            instance.enable();
            instance.show();
          }
        } else {
          // See if we can fetch this
          fetch(url.split('#')[0])
          .then(res => res.text())
          .then(html => {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(html, "text/html");
            const note = htmlDoc.getElementById(id);
            if (note !== null) {
              const html = processXRef(id, note);
              instance.setContent(html);
            } 
          }).finally(() => {
            instance.enable();
            instance.show();
          });
        }
      } else {
        // See if we can fetch a full url (with no hash to target)
        // This is a special case and we should probably do some content thinning / targeting
        fetch(url)
        .then(res => res.text())
        .then(html => {
          const parser = new DOMParser();
          const htmlDoc = parser.parseFromString(html, "text/html");
          const note = htmlDoc.querySelector('main.content');
          if (note !== null) {
            // This should only happen for chapter cross references
            // (since there is no id in the URL)
            // remove the first header
            if (note.children.length > 0 && note.children[0].tagName === "HEADER") {
              note.children[0].remove();
            }
            const html = processXRef(null, note);
            instance.setContent(html);
          } 
        }).finally(() => {
          instance.enable();
          instance.show();
        });
      }
    }, function(instance) {
    });
  }
      let selectedAnnoteEl;
      const selectorForAnnotation = ( cell, annotation) => {
        let cellAttr = 'data-code-cell="' + cell + '"';
        let lineAttr = 'data-code-annotation="' +  annotation + '"';
        const selector = 'span[' + cellAttr + '][' + lineAttr + ']';
        return selector;
      }
      const selectCodeLines = (annoteEl) => {
        const doc = window.document;
        const targetCell = annoteEl.getAttribute("data-target-cell");
        const targetAnnotation = annoteEl.getAttribute("data-target-annotation");
        const annoteSpan = window.document.querySelector(selectorForAnnotation(targetCell, targetAnnotation));
        const lines = annoteSpan.getAttribute("data-code-lines").split(",");
        const lineIds = lines.map((line) => {
          return targetCell + "-" + line;
        })
        let top = null;
        let height = null;
        let parent = null;
        if (lineIds.length > 0) {
            //compute the position of the single el (top and bottom and make a div)
            const el = window.document.getElementById(lineIds[0]);
            top = el.offsetTop;
            height = el.offsetHeight;
            parent = el.parentElement.parentElement;
          if (lineIds.length > 1) {
            const lastEl = window.document.getElementById(lineIds[lineIds.length - 1]);
            const bottom = lastEl.offsetTop + lastEl.offsetHeight;
            height = bottom - top;
          }
          if (top !== null && height !== null && parent !== null) {
            // cook up a div (if necessary) and position it 
            let div = window.document.getElementById("code-annotation-line-highlight");
            if (div === null) {
              div = window.document.createElement("div");
              div.setAttribute("id", "code-annotation-line-highlight");
              div.style.position = 'absolute';
              parent.appendChild(div);
            }
            div.style.top = top - 2 + "px";
            div.style.height = height + 4 + "px";
            div.style.left = 0;
            let gutterDiv = window.document.getElementById("code-annotation-line-highlight-gutter");
            if (gutterDiv === null) {
              gutterDiv = window.document.createElement("div");
              gutterDiv.setAttribute("id", "code-annotation-line-highlight-gutter");
              gutterDiv.style.position = 'absolute';
              const codeCell = window.document.getElementById(targetCell);
              const gutter = codeCell.querySelector('.code-annotation-gutter');
              gutter.appendChild(gutterDiv);
            }
            gutterDiv.style.top = top - 2 + "px";
            gutterDiv.style.height = height + 4 + "px";
          }
          selectedAnnoteEl = annoteEl;
        }
      };
      const unselectCodeLines = () => {
        const elementsIds = ["code-annotation-line-highlight", "code-annotation-line-highlight-gutter"];
        elementsIds.forEach((elId) => {
          const div = window.document.getElementById(elId);
          if (div) {
            div.remove();
          }
        });
        selectedAnnoteEl = undefined;
      };
        // Handle positioning of the toggle
    window.addEventListener(
      "resize",
      throttle(() => {
        elRect = undefined;
        if (selectedAnnoteEl) {
          selectCodeLines(selectedAnnoteEl);
        }
      }, 10)
    );
    function throttle(fn, ms) {
    let throttle = false;
    let timer;
      return (...args) => {
        if(!throttle) { // first call gets through
            fn.apply(this, args);
            throttle = true;
        } else { // all the others get throttled
            if(timer) clearTimeout(timer); // cancel #2
            timer = setTimeout(() => {
              fn.apply(this, args);
              timer = throttle = false;
            }, ms);
        }
      };
    }
      // Attach click handler to the DT
      const annoteDls = window.document.querySelectorAll('dt[data-target-cell]');
      for (const annoteDlNode of annoteDls) {
        annoteDlNode.addEventListener('click', (event) => {
          const clickedEl = event.target;
          if (clickedEl !== selectedAnnoteEl) {
            unselectCodeLines();
            const activeEl = window.document.querySelector('dt[data-target-cell].code-annotation-active');
            if (activeEl) {
              activeEl.classList.remove('code-annotation-active');
            }
            selectCodeLines(clickedEl);
            clickedEl.classList.add('code-annotation-active');
          } else {
            // Unselect the line
            unselectCodeLines();
            clickedEl.classList.remove('code-annotation-active');
          }
        });
      }
  const findCites = (el) => {
    const parentEl = el.parentElement;
    if (parentEl) {
      const cites = parentEl.dataset.cites;
      if (cites) {
        return {
          el,
          cites: cites.split(' ')
        };
      } else {
        return findCites(el.parentElement)
      }
    } else {
      return undefined;
    }
  };
  var bibliorefs = window.document.querySelectorAll('a[role="doc-biblioref"]');
  for (var i=0; i<bibliorefs.length; i++) {
    const ref = bibliorefs[i];
    const citeInfo = findCites(ref);
    if (citeInfo) {
      tippyHover(citeInfo.el, function() {
        var popup = window.document.createElement('div');
        citeInfo.cites.forEach(function(cite) {
          var citeDiv = window.document.createElement('div');
          citeDiv.classList.add('hanging-indent');
          citeDiv.classList.add('csl-entry');
          var biblioDiv = window.document.getElementById('ref-' + cite);
          if (biblioDiv) {
            citeDiv.innerHTML = biblioDiv.innerHTML;
          }
          popup.appendChild(citeDiv);
        });
        return popup.innerHTML;
      });
    }
  }
});
</script>
</div> <!-- /content -->




</body></html>