---
title: "13类行业分类回归分析"
author: "ESG China Research"
date: "`r Sys.Date()`"
format: 
  html:
    toc: true
    toc-depth: 3
    code-fold: false
    theme: cosmo
editor: visual
---

# 数据准备

## 加载必要的库和数据

```{r setup, message=FALSE, warning=FALSE}
# 加载必要的库
library(plm)
library(lme4)
library(lmerTest)
library(stargazer)
library(sjPlot)
library(dplyr)
library(knitr)
library(kableExtra)
library(stringr)

# 加载数据
load("dta1_20240903.RData")

# 重新计算Age变量，使其随年份变化
library(lubridate)
library(dplyr)
dta1 <- dta1 %>%
  mutate(
    EndYear = ymd(EndYear),
    Year = year(EndYear),
    Age = Year - EstablishYear
  )

# 检查数据结构
cat("原始数据概览:\n")
cat("总观测数:", nrow(dta1), "\n")
cat("唯一公司数:", length(unique(dta1$Symbol)), "\n")
cat("原始行业数:", length(unique(dta1$IndustryName)), "\n")




```

## 创建13类行业分类

```{r industry_classification_11}
# 创建13类行业分类
dta1$industry_type13 <- case_when(
  # 1. Agriculture, Forestry, Livestock Farming, Fishery
  dta1$IndustryName %in% c("农业", "林业", "畜牧业", "渔业", 
                           "农、林、牧、渔服务业") ~ "Agriculture, Forestry, Livestock Farming, Fishery",
  
  # 2. Mining
  dta1$IndustryName %in% c("煤炭开采和洗选业", "石油和天然气开采业", 
                           "黑色金属矿采选业", "有色金属矿采选业", 
                           "开采辅助活动") ~ "Mining",
  
  # 3. Manufacturing
  dta1$IndustryName %in% c("医药制造业", "化学原料及化学制品制造业", 
                           "化学纤维制造业", "非金属矿物制品业", 
                           "黑色金属冶炼及压延加工业", "有色金属冶炼及压延加工业", 
                           "金属制品业", "通用设备制造业", 
                           "专用设备制造业", "铁路、船舶、航空航天和其它运输设备制造业", 
                           "汽车制造业", "电气机械及器材制造业", 
                           "计算机、通信和其他电子设备制造业", "仪器仪表制造业", 
                           "其他制造业", "食品制造业", 
                           "酒、饮料和精制茶制造业", "农副食品加工业", 
                           "纺织业", "纺织服装、服饰业", 
                           "皮革、毛皮、羽毛及其制品和制鞋业", "木材加工及木、竹、藤、棕、草制品业", 
                           "造纸及纸制品业", "印刷和记录媒介复制业", 
                           "橡胶和塑料制品业", "家具制造业", 
                           "废弃资源综合利用业", "文教、工美、体育和娱乐用品制造业", 
                           "金属制品、机械和设备修理业", "石油加工、炼焦及核燃料加工业") ~ "Manufacturing",
  
  # 4. Electric Power, Gas, and Water Production and Supply
  dta1$IndustryName %in% c("电力、热力生产和供应业", "燃气生产和供应业", 
                           "水的生产和供应业") ~ "Electric Power, Gas, and Water Production and Supply",
  
  # 5. Construction
  dta1$IndustryName %in% c("房屋建筑业", "建筑安装业", 
                           "土木工程建筑业", "建筑装饰和其他建筑业") ~ "Construction",
  
  # 6. Transport and Storage
  dta1$IndustryName %in% c("装卸搬运和运输代理业", "道路运输业", 
                           "水上运输业", "铁路运输业", 
                           "航空运输业", "仓储业") ~ "Transport and Storage",
  
  # 7. Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业", "专业技术服务业","互联网和相关服务", 
                           "研究和试验发展", "科技推广和应用服务业") ~ "Information Technology",
  
  # 8. Wholesale and Retail Trade
  dta1$IndustryName %in% c("租赁业", "批发业", "零售业") ~ "Wholesale and Retail Trade",
  
  # 9. Finance and Insurance
  dta1$IndustryName %in% c("货币金融服务", "资本市场服务", 
                           "其他金融业", "保险业"
                           ) ~ "Finance and Insurance",
  
  # 10. Real Estate
  dta1$IndustryName %in% c("房地产业") ~ "Real Estate",
  
  # 11. Social Service
  dta1$IndustryName %in% c("教育", "卫生", 
                           "公共设施管理业", "生态保护和环境治理业", 
                           "居民服务业") ~ "Social Service",
  
  # 12. Communication and Culture
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务", "广播、电视、电影和影视录音制作业", 
                           "新闻和出版业", "文化艺术业", 
                           "体育", "邮政业") ~ "Communication and Culture",
  
  # 13. Others
  TRUE ~ "Others"
)



# 显示分类结果
cat("13类分类结果:\n")
industry_table <- table(dta1$industry_type13, useNA = "always")
print(industry_table)

# 分析NA值的详细情况
na_data <- dta1 %>% filter(is.na(industry_type13))
cat("\n=== NA值分析 ===\n")
cat("NA观测数:", nrow(na_data), "\n")
cat("NA占总观测数的比例:", round(nrow(na_data)/nrow(dta1)*100, 2), "%\n")
cat("NA涉及的唯一公司数:", length(unique(na_data$Symbol)), "\n")
cat("NA涉及的唯一行业数:", length(unique(na_data$IndustryName)), "\n")

# 显示所有未分类的行业名称
cat("\n未分类的行业名称:\n")
na_industries <- na_data %>%
  group_by(IndustryName) %>%
  summarise(count = n(), .groups = 'drop') %>%
  arrange(desc(count))
print(na_industries)

# 创建去除NA的数据集用于分析
dta1_13class <- dta1 %>% filter(!is.na(industry_type13))
cat("\n去除NA后的观测数:", nrow(dta1_13class), "\n")
cat("去除NA后的公司数:", length(unique(dta1_13class$Symbol)), "\n")
```

# 模型比较分析：添加vs不添加industry_type13

## 第一步：基准模型（不包含行业分类）

```{r baseline_models_without_industry}
# ===== 基准模型组：不包含任何行业效应 =====
# 这些模型只包含省份/城市随机效应，作为比较的基准

# P3系列基准模型
baseline_p3_1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                     RegisterCapital_log +   (1 | PROVINCE/CITY),
                     data = dta1_13class)

baseline_p3_2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage +
                     RegisterCapital_log +   (1 | PROVINCE/CITY),
                     data = dta1_13class)

baseline_p3_3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                     ESG_Rate + ROA + Leverage + RegisterCapital_log +   (1 | PROVINCE/CITY),
                     data = dta1_13class)

# P4系列基准模型 - 中央政治关联
baseline_p4_central_1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage +
                             RegisterCapital_log +   (1 | PROVINCE/CITY),
                             data = dta1_13class)

baseline_p4_central_2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                             ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                             (1 | PROVINCE/CITY),
                             data = dta1_13class)

# P4系列基准模型 - 地方政治关联
baseline_p4_local_1 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage +
                           RegisterCapital_log +   (1 | PROVINCE/CITY),
                           data = dta1_13class)

baseline_p4_local_2 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                           ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                           (1 | PROVINCE/CITY),
                           data = dta1_13class)

cat("=== 基准模型创建完成 ===\n")
cat("已创建7个基准模型（不包含行业效应）\n")
```

## 第二步：包含行业固定效应的对比模型

```{r industry_fixed_effects_models}
# ===== 行业固定效应模型组 =====
# 这些模型在基准模型基础上添加industry_type13作为固定效应

# P3系列 + 行业固定效应
fixed_p3_1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                   RegisterCapital_log +   as.factor(industry_type13) + (1 | PROVINCE/CITY),
                   data = dta1_13class)

fixed_p3_2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage +
                   RegisterCapital_log +   as.factor(industry_type13) + (1 | PROVINCE/CITY),
                   data = dta1_13class)

fixed_p3_3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                   ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                   as.factor(industry_type13) + (1 | PROVINCE/CITY),
                   data = dta1_13class)

# P4系列 + 行业固定效应 - 中央政治关联
fixed_p4_central_1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection +
                          ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                          as.factor(industry_type13) + (1 | PROVINCE/CITY),
                          data = dta1_13class)

fixed_p4_central_2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                          ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                          as.factor(industry_type13) + (1 | PROVINCE/CITY),
                          data = dta1_13class)

# P4系列 + 行业固定效应 - 地方政治关联
fixed_p4_local_1 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection +
                        ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                        as.factor(industry_type13) + (1 | PROVINCE/CITY),
                        data = dta1_13class)

fixed_p4_local_2 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                        ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                        as.factor(industry_type13) + (1 | PROVINCE/CITY),
                        data = dta1_13class)

cat("=== 行业固定效应模型创建完成 ===\n")
cat("已创建7个包含行业固定效应的模型\n")
```

## 第三步：包含行业随机效应的对比模型

```{r industry_random_effects_models}
# ===== 行业随机效应模型组 =====
# 这些模型在基准模型基础上添加industry_type13作为随机效应

# P3系列 + 行业随机效应
random_p3_1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                   RegisterCapital_log +   (1 | PROVINCE/CITY) + (1 | industry_type13),
                   data = dta1_13class)

random_p3_2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage +
                   RegisterCapital_log +   (1 | PROVINCE/CITY) + (1 | industry_type13),
                   data = dta1_13class)

random_p3_3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                   ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                   (1 | PROVINCE/CITY) + (1 | industry_type13),
                   data = dta1_13class)

# P4系列 + 行业随机效应 - 中央政治关联
random_p4_central_1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage +
                           RegisterCapital_log +   (1 | PROVINCE/CITY) + (1 | industry_type13),
                           data = dta1_13class)

random_p4_central_2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                           ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                           (1 | PROVINCE/CITY) + (1 | industry_type13),
                           data = dta1_13class)

# P4系列 + 行业随机效应 - 地方政治关联
random_p4_local_1 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage +
                         RegisterCapital_log +   (1 | PROVINCE/CITY) + (1 | industry_type13),
                         data = dta1_13class)

random_p4_local_2 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                         ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                         (1 | PROVINCE/CITY) + (1 | industry_type13),
                         data = dta1_13class)

cat("=== 行业随机效应模型创建完成 ===\n")
cat("已创建7个包含行业随机效应的模型\n")
```

## 第四步：模型性能指标计算

```{r model_performance_metrics}
# ===== 计算所有模型的性能指标 =====
library(performance)
library(MuMIn)

# 创建模型列表
baseline_models <- list(
  "P3_1_baseline" = baseline_p3_1,
  "P3_2_baseline" = baseline_p3_2,
  "P3_3_baseline" = baseline_p3_3,
  "P4_central_1_baseline" = baseline_p4_central_1,
  "P4_central_2_baseline" = baseline_p4_central_2,
  "P4_local_1_baseline" = baseline_p4_local_1,
  "P4_local_2_baseline" = baseline_p4_local_2
)

fixed_models <- list(
  "P3_1_fixed" = fixed_p3_1,
  "P3_2_fixed" = fixed_p3_2,
  "P3_3_fixed" = fixed_p3_3,
  "P4_central_1_fixed" = fixed_p4_central_1,
  "P4_central_2_fixed" = fixed_p4_central_2,
  "P4_local_1_fixed" = fixed_p4_local_1,
  "P4_local_2_fixed" = fixed_p4_local_2
)

random_models <- list(
  "P3_1_random" = random_p3_1,
  "P3_2_random" = random_p3_2,
  "P3_3_random" = random_p3_3,
  "P4_central_1_random" = random_p4_central_1,
  "P4_central_2_random" = random_p4_central_2,
  "P4_local_1_random" = random_p4_local_1,
  "P4_local_2_random" = random_p4_local_2
)

# 计算性能指标的函数
calculate_model_metrics <- function(model_list, model_type) {
  results <- data.frame()

  for(i in 1:length(model_list)) {
    model <- model_list[[i]]
    model_name <- names(model_list)[i]

    # 基本信息
    aic_val <- AIC(model)
    bic_val <- BIC(model)
    loglik_val <- as.numeric(logLik(model))

    # R-squared
    r2_vals <- r.squaredGLMM(model)
    r2_marginal <- r2_vals[1]
    r2_conditional <- r2_vals[2]

    # ICC
    icc_val <- performance::icc(model)$ICC_adjusted

    # 自由度和观测数
    df_val <- attr(logLik(model), "df")
    nobs_val <- nobs(model)

    # 组合结果
    row_result <- data.frame(
      Model = model_name,
      Type = model_type,
      AIC = aic_val,
      BIC = bic_val,
      LogLik = loglik_val,
      R2_marginal = r2_marginal,
      R2_conditional = r2_conditional,
      ICC = icc_val,
      DF = df_val,
      N_obs = nobs_val,
      stringsAsFactors = FALSE
    )

    results <- rbind(results, row_result)
  }

  return(results)
}

# 计算所有模型的指标
baseline_metrics <- calculate_model_metrics(baseline_models, "Baseline")
fixed_metrics <- calculate_model_metrics(fixed_models, "Fixed_Industry")
random_metrics <- calculate_model_metrics(random_models, "Random_Industry")

# 合并所有结果
all_metrics <- rbind(baseline_metrics, fixed_metrics, random_metrics)

# 显示结果
cat("=== 模型性能指标计算完成 ===\n")
print(all_metrics)
```

## 第五步：似然比检验

```{r likelihood_ratio_tests}
# ===== 似然比检验：比较嵌套模型 =====

# 创建比较函数
perform_lr_test <- function(baseline_model, comparison_model, comparison_name) {
  lr_test <- anova(baseline_model, comparison_model)

  result <- data.frame(
    Comparison = comparison_name,
    Chi_square = lr_test$Chisq[2],
    DF_diff = lr_test$Df[2],
    P_value = lr_test$`Pr(>Chisq)`[2],
    Significant = lr_test$`Pr(>Chisq)`[2] < 0.05,
    stringsAsFactors = FALSE
  )

  return(result)
}

# 进行所有的似然比检验
lr_results <- data.frame()

# P3系列检验
lr_results <- rbind(lr_results, perform_lr_test(baseline_p3_1, fixed_p3_1, "P3_1: Baseline vs Fixed"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p3_1, random_p3_1, "P3_1: Baseline vs Random"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p3_2, fixed_p3_2, "P3_2: Baseline vs Fixed"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p3_2, random_p3_2, "P3_2: Baseline vs Random"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p3_3, fixed_p3_3, "P3_3: Baseline vs Fixed"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p3_3, random_p3_3, "P3_3: Baseline vs Random"))

# P4中央系列检验
lr_results <- rbind(lr_results, perform_lr_test(baseline_p4_central_1, fixed_p4_central_1, "P4_Central_1: Baseline vs Fixed"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p4_central_1, random_p4_central_1, "P4_Central_1: Baseline vs Random"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p4_central_2, fixed_p4_central_2, "P4_Central_2: Baseline vs Fixed"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p4_central_2, random_p4_central_2, "P4_Central_2: Baseline vs Random"))

# P4地方系列检验
lr_results <- rbind(lr_results, perform_lr_test(baseline_p4_local_1, fixed_p4_local_1, "P4_Local_1: Baseline vs Fixed"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p4_local_1, random_p4_local_1, "P4_Local_1: Baseline vs Random"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p4_local_2, fixed_p4_local_2, "P4_Local_2: Baseline vs Fixed"))
lr_results <- rbind(lr_results, perform_lr_test(baseline_p4_local_2, random_p4_local_2, "P4_Local_2: Baseline vs Random"))

# 显示似然比检验结果
cat("=== 似然比检验结果 ===\n")
print(lr_results)

# 统计显著性结果
significant_improvements <- sum(lr_results$Significant)
total_comparisons <- nrow(lr_results)

cat("\n=== 似然比检验总结 ===\n")
cat("总比较次数:", total_comparisons, "\n")
cat("显著改善的模型数:", significant_improvements, "\n")
cat("显著改善比例:", round(significant_improvements/total_comparisons*100, 2), "%\n")
```

## 第六步：创建模型比较汇总表格

```{r model_comparison_tables}
# ===== 创建详细的模型比较表格 =====

# 重新整理数据以便比较
library(dplyr)
library(kableExtra)

# 为每个模型组创建比较表
create_comparison_table <- function(baseline_name, fixed_name, random_name) {
  baseline_row <- all_metrics[all_metrics$Model == baseline_name, ]
  fixed_row <- all_metrics[all_metrics$Model == fixed_name, ]
  random_row <- all_metrics[all_metrics$Model == random_name, ]

  comparison_df <- rbind(baseline_row, fixed_row, random_row)

  # 计算相对于基准模型的变化
  comparison_df$AIC_diff <- comparison_df$AIC - baseline_row$AIC
  comparison_df$BIC_diff <- comparison_df$BIC - baseline_row$BIC
  comparison_df$LogLik_diff <- comparison_df$LogLik - baseline_row$LogLik
  comparison_df$R2_marginal_diff <- comparison_df$R2_marginal - baseline_row$R2_marginal
  comparison_df$R2_conditional_diff <- comparison_df$R2_conditional - baseline_row$R2_conditional

  return(comparison_df)
}

# P3系列比较
cat("=== P3系列模型比较 ===\n")

p3_1_comparison <- create_comparison_table("P3_1_baseline", "P3_1_fixed", "P3_1_random")
p3_2_comparison <- create_comparison_table("P3_2_baseline", "P3_2_fixed", "P3_2_random")
p3_3_comparison <- create_comparison_table("P3_3_baseline", "P3_3_fixed", "P3_3_random")

print("P3_1 模型比较:")
print(p3_1_comparison[, c("Type", "AIC", "BIC", "LogLik", "R2_marginal", "R2_conditional", "ICC")])

print("P3_2 模型比较:")
print(p3_2_comparison[, c("Type", "AIC", "BIC", "LogLik", "R2_marginal", "R2_conditional", "ICC")])

print("P3_3 模型比较:")
print(p3_3_comparison[, c("Type", "AIC", "BIC", "LogLik", "R2_marginal", "R2_conditional", "ICC")])

# P4系列比较
cat("\n=== P4系列模型比较 ===\n")

p4_central_1_comparison <- create_comparison_table("P4_central_1_baseline", "P4_central_1_fixed", "P4_central_1_random")
p4_central_2_comparison <- create_comparison_table("P4_central_2_baseline", "P4_central_2_fixed", "P4_central_2_random")
p4_local_1_comparison <- create_comparison_table("P4_local_1_baseline", "P4_local_1_fixed", "P4_local_1_random")
p4_local_2_comparison <- create_comparison_table("P4_local_2_baseline", "P4_local_2_fixed", "P4_local_2_random")

print("P4_Central_1 模型比较:")
print(p4_central_1_comparison[, c("Type", "AIC", "BIC", "LogLik", "R2_marginal", "R2_conditional", "ICC")])

print("P4_Central_2 模型比较:")
print(p4_central_2_comparison[, c("Type", "AIC", "BIC", "LogLik", "R2_marginal", "R2_conditional", "ICC")])

print("P4_Local_1 模型比较:")
print(p4_local_1_comparison[, c("Type", "AIC", "BIC", "LogLik", "R2_marginal", "R2_conditional", "ICC")])

print("P4_Local_2 模型比较:")
print(p4_local_2_comparison[, c("Type", "AIC", "BIC", "LogLik", "R2_marginal", "R2_conditional", "ICC")])
```

## 第七步：结果分析和可视化

```{r results_analysis_visualization}
# ===== 结果分析和可视化 =====
library(ggplot2)
library(gridExtra)

# 1. AIC/BIC比较图
aic_bic_plot <- all_metrics %>%
  select(Model, Type, AIC, BIC) %>%
  tidyr::pivot_longer(cols = c(AIC, BIC), names_to = "Metric", values_to = "Value") %>%
  mutate(Model_Group = gsub("_baseline|_fixed|_random", "", Model)) %>%
  ggplot(aes(x = Model_Group, y = Value, fill = Type)) +
  geom_bar(stat = "identity", position = "dodge") +
  facet_wrap(~Metric, scales = "free_y") +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
  labs(title = "AIC和BIC比较",
       subtitle = "较低的值表示更好的模型拟合",
       x = "模型组", y = "信息准则值") +
  scale_fill_manual(values = c("Baseline" = "#E74C3C",
                              "Fixed_Industry" = "#3498DB",
                              "Random_Industry" = "#2ECC71"))

print(aic_bic_plot)

# 2. R²比较图
r2_plot <- all_metrics %>%
  select(Model, Type, R2_marginal, R2_conditional) %>%
  tidyr::pivot_longer(cols = c(R2_marginal, R2_conditional), names_to = "R2_Type", values_to = "Value") %>%
  mutate(Model_Group = gsub("_baseline|_fixed|_random", "", Model)) %>%
  ggplot(aes(x = Model_Group, y = Value, fill = Type)) +
  geom_bar(stat = "identity", position = "dodge") +
  facet_wrap(~R2_Type, scales = "free_y") +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
  labs(title = "R²比较",
       subtitle = "较高的值表示更好的解释能力",
       x = "模型组", y = "R²值") +
  scale_fill_manual(values = c("Baseline" = "#E74C3C",
                              "Fixed_Industry" = "#3498DB",
                              "Random_Industry" = "#2ECC71"))

print(r2_plot)

# 3. ICC比较图
icc_plot <- all_metrics %>%
  mutate(Model_Group = gsub("_baseline|_fixed|_random", "", Model)) %>%
  ggplot(aes(x = Model_Group, y = ICC, fill = Type)) +
  geom_bar(stat = "identity", position = "dodge") +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
  labs(title = "ICC (组内相关系数) 比较",
       subtitle = "显示随机效应的重要性",
       x = "模型组", y = "ICC值") +
  scale_fill_manual(values = c("Baseline" = "#E74C3C",
                              "Fixed_Industry" = "#3498DB",
                              "Random_Industry" = "#2ECC71"))

print(icc_plot)

# 4. 创建性能改善/恶化统计
performance_summary <- all_metrics %>%
  mutate(Model_Group = gsub("_baseline|_fixed|_random", "", Model)) %>%
  group_by(Model_Group) %>%
  summarise(
    Baseline_AIC = AIC[Type == "Baseline"],
    Fixed_AIC = AIC[Type == "Fixed_Industry"],
    Random_AIC = AIC[Type == "Random_Industry"],
    Fixed_AIC_Change = Fixed_AIC - Baseline_AIC,
    Random_AIC_Change = Random_AIC - Baseline_AIC,
    Fixed_Worse = Fixed_AIC_Change > 0,
    Random_Worse = Random_AIC_Change > 0,
    .groups = 'drop'
  )

cat("\n=== 性能变化总结 ===\n")
print(performance_summary)

# 统计有多少模型在添加行业分类后性能变差
fixed_worse_count <- sum(performance_summary$Fixed_Worse)
random_worse_count <- sum(performance_summary$Random_Worse)
total_models <- nrow(performance_summary)

cat("\n=== 最终结果统计 ===\n")
cat("总模型组数:", total_models, "\n")
cat("添加固定行业效应后AIC变差的模型数:", fixed_worse_count, "\n")
cat("添加随机行业效应后AIC变差的模型数:", random_worse_count, "\n")
cat("固定效应恶化比例:", round(fixed_worse_count/total_models*100, 2), "%\n")
cat("随机效应恶化比例:", round(random_worse_count/total_models*100, 2), "%\n")
```

## 第八步：详细结论分析

```{r detailed_conclusions}
# ===== 详细结论分析 =====

cat("=== 模型比较分析结论 ===\n\n")

# 1. 整体性能比较
cat("1. 整体性能比较:\n")
cat("   - 我们比较了21个模型（7个基准模型 + 7个固定效应模型 + 7个随机效应模型）\n")
cat("   - 基准模型：只包含省份/城市随机效应，不包含行业分类\n")
cat("   - 固定效应模型：在基准模型基础上添加industry_type13作为固定效应\n")
cat("   - 随机效应模型：在基准模型基础上添加industry_type13作为随机效应\n\n")

# 2. AIC/BIC分析
aic_improvements_fixed <- sum(performance_summary$Fixed_AIC_Change < 0)
aic_improvements_random <- sum(performance_summary$Random_AIC_Change < 0)

cat("2. AIC/BIC信息准则分析:\n")
cat("   - AIC/BIC越低表示模型拟合越好\n")
cat("   - 固定效应模型中AIC改善的模型数:", aic_improvements_fixed, "/", total_models, "\n")
cat("   - 随机效应模型中AIC改善的模型数:", aic_improvements_random, "/", total_models, "\n")
cat("   - 固定效应改善比例:", round(aic_improvements_fixed/total_models*100, 2), "%\n")
cat("   - 随机效应改善比例:", round(aic_improvements_random/total_models*100, 2), "%\n\n")

# 3. R²分析
r2_analysis <- all_metrics %>%
  mutate(Model_Group = gsub("_baseline|_fixed|_random", "", Model)) %>%
  group_by(Model_Group) %>%
  summarise(
    Baseline_R2_marginal = R2_marginal[Type == "Baseline"],
    Fixed_R2_marginal = R2_marginal[Type == "Fixed_Industry"],
    Random_R2_marginal = R2_marginal[Type == "Random_Industry"],
    Fixed_R2_improvement = Fixed_R2_marginal - Baseline_R2_marginal,
    Random_R2_improvement = Random_R2_marginal - Baseline_R2_marginal,
    .groups = 'drop'
  )

r2_fixed_improvements <- sum(r2_analysis$Fixed_R2_improvement > 0)
r2_random_improvements <- sum(r2_analysis$Random_R2_improvement > 0)

cat("3. R²解释能力分析:\n")
cat("   - R²越高表示模型解释能力越强\n")
cat("   - 固定效应模型中边际R²改善的模型数:", r2_fixed_improvements, "/", total_models, "\n")
cat("   - 随机效应模型中边际R²改善的模型数:", r2_random_improvements, "/", total_models, "\n")
cat("   - 固定效应R²改善比例:", round(r2_fixed_improvements/total_models*100, 2), "%\n")
cat("   - 随机效应R²改善比例:", round(r2_random_improvements/total_models*100, 2), "%\n\n")

# 4. 似然比检验分析
significant_fixed <- sum(grepl("Fixed", lr_results$Comparison) & lr_results$Significant)
significant_random <- sum(grepl("Random", lr_results$Comparison) & lr_results$Significant)
total_fixed_tests <- sum(grepl("Fixed", lr_results$Comparison))
total_random_tests <- sum(grepl("Random", lr_results$Comparison))

cat("4. 似然比检验分析:\n")
cat("   - 检验添加行业分类是否显著改善模型拟合\n")
cat("   - 固定效应显著改善的模型数:", significant_fixed, "/", total_fixed_tests, "\n")
cat("   - 随机效应显著改善的模型数:", significant_random, "/", total_random_tests, "\n")
cat("   - 固定效应显著改善比例:", round(significant_fixed/total_fixed_tests*100, 2), "%\n")
cat("   - 随机效应显著改善比例:", round(significant_random/total_random_tests*100, 2), "%\n\n")

# 5. 最终结论
cat("5. 最终结论:\n")

if(fixed_worse_count > total_models/2) {
  cat("   ✓ 添加industry_type13作为固定效应在大多数情况下降低了模型性能\n")
} else {
  cat("   ✗ 添加industry_type13作为固定效应在大多数情况下改善了模型性能\n")
}

if(random_worse_count > total_models/2) {
  cat("   ✓ 添加industry_type13作为随机效应在大多数情况下降低了模型性能\n")
} else {
  cat("   ✗ 添加industry_type13作为随机效应在大多数情况下改善了模型性能\n")
}

if(aic_improvements_fixed < total_models/2 && aic_improvements_random < total_models/2) {
  cat("   ✓ 根据AIC准则，添加行业分类总体上降低了模型拟合质量\n")
} else {
  cat("   ✗ 根据AIC准则，添加行业分类总体上改善了模型拟合质量\n")
}

if(significant_fixed < total_fixed_tests/2 && significant_random < total_random_tests/2) {
  cat("   ✓ 似然比检验显示，大多数情况下添加行业分类没有显著改善模型\n")
} else {
  cat("   ✗ 似然比检验显示，大多数情况下添加行业分类显著改善了模型\n")
}

cat("\n")
cat("=== 研究假设验证 ===\n")
cat("研究假设：添加industry_type13会降低模型性能\n")

# 计算支持假设的证据比例
evidence_count <- 0
total_evidence <- 4

if(fixed_worse_count > total_models/2) evidence_count <- evidence_count + 1
if(random_worse_count > total_models/2) evidence_count <- evidence_count + 1
if(aic_improvements_fixed < total_models/2 && aic_improvements_random < total_models/2) evidence_count <- evidence_count + 1
if(significant_fixed < total_fixed_tests/2 && significant_random < total_random_tests/2) evidence_count <- evidence_count + 1

support_percentage <- round(evidence_count/total_evidence*100, 2)

cat("支持假设的证据比例:", support_percentage, "%\n")

if(support_percentage >= 75) {
  cat("结论：强烈支持研究假设 - 添加industry_type13显著降低了模型性能\n")
} else if(support_percentage >= 50) {
  cat("结论：部分支持研究假设 - 添加industry_type13在某些方面降低了模型性能\n")
} else {
  cat("结论：不支持研究假设 - 添加industry_type13总体上改善了模型性能\n")
}
```

# 原始混合效应模型 (LMER) - 保留原有分析

## 基础混合效应模型

```{r lmer_basic_11class_p3}
# 基础混合效应模型 - 省份随机效应
p3mix1_11 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log +   (1 | PROVINCE/CITY), 
                  data = dta1_13class)

p3mix2_11 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log +   (1 | PROVINCE/CITY), 
                  data = dta1_13class)

p3mix3_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + 
                  ESG_Rate + ROA + Leverage + RegisterCapital_log +   (1 | PROVINCE/CITY), 
                  data = dta1_13class)

# 使用tab_model输出结果
tab_model(p3mix1_11, p3mix2_11, p3mix3_11,
          title = "P3 Mixed Effects Models (11-Class) - Province Random Effects",
          dv.labels = "Environmental Information Disclosure",
          rm.terms = c("as.factor"),
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```

```{r lmer_basic_11class_p4}
# 中央/地方政治关联的混合效应模型
p4mix1_11_1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log +   (1 | PROVINCE/CITY), 
                  data = dta1_13class)

p4mix2_11_1 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + 
                  ESG_Rate + ROA + Leverage + RegisterCapital_log +   
                  (1 | PROVINCE/CITY), 
                  data = dta1_13class)

p4mix3_11_1 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log +   (1 | PROVINCE/CITY), 
                  data = dta1_13class)

p4mix4_11_1 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                  ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                  (1 | PROVINCE/CITY) ,
                  data = dta1_13class)
```

```{r}
# 输出结果

tab_model(p4mix1_11_1, p4mix2_11_1, p4mix3_11_1, p4mix4_11_1,
          title = "P4 Mixed Effects Models (11-Class) - Central vs Local Connections",
          dv.labels = "Environmental Information Disclosure",
          rm.terms = c("as.factor"),
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```

## 包含行业随机效应的混合效应模型

```{r}
p3mix1_slope_11 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num +
                       ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                       (1 | PROVINCE/CITY) + (0 + connection_num | industry_type13),
                       data = dta1_13class)

p3mix2_slope_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection +
                       ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                       (1 | PROVINCE/CITY) + (0 + connection_num | industry_type13),
                       data = dta1_13class)

p3mix3_slope_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                       ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                       (1 | PROVINCE/CITY) + (0 + connection_num | industry_type13),
                       data = dta1_13class)


# 中央/地方政治关联的混合效应模型
p4mix1_11 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log +   (1 | PROVINCE/CITY) + (1 | industry_type13), 
                  data = dta1_13class)

p4mix2_11 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + 
                  ESG_Rate + ROA + Leverage + RegisterCapital_log +   
                  (1 | PROVINCE/CITY) + (0 + central_connection | industry_type13), 
                  data = dta1_13class)

p4mix3_11 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage + 
                  RegisterCapital_log +   (1 | PROVINCE/CITY) + (1 | industry_type13), 
                  data = dta1_13class)

p4mix4_11 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                  ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                  (1 | PROVINCE/CITY) + (0 + local_connection | industry_type13),
                  data = dta1_13class)


# 输出随机斜率模型结果
tab_model(p3mix1_slope_11, p3mix2_slope_11, p3mix3_slope_11,
          title = "Random Slope Models (11-Class) - Industry-specific Interaction Effects",
          dv.labels = "Environmental Information Disclosure",
          rm.terms = c("as.factor"),
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)

tab_model(p4mix1_11, p4mix2_11, p4mix3_11, p4mix4_11,
          title = "P4 Mixed Effects Models (11-Class) - Central vs Local Connections",
          dv.labels = "Environmental Information Disclosure",
          rm.terms = c("as.factor"),
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)


```

## 包含行业固定效应的混合效应模型

```{r lmer_fixed_industry_11class}
# 包含行业固定效应的混合效应模型
p3mix1_fixed_industry_11 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                                RegisterCapital_log +   as.factor(industry_type13) + (1 | PROVINCE/CITY),
                                data = dta1_13class)

p3mix2_fixed_industry_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection + ESG_Rate + ROA + Leverage +
                                RegisterCapital_log +   as.factor(industry_type13) + (1 | PROVINCE/CITY),
                                data = dta1_13class)


p3mix3_fixed_industry_11 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                                   ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                                   as.factor(industry_type13) + (1 | PROVINCE/CITY),
                                   data = dta1_13class)

p4mix1_fixed_central_11 <- lmer(Environmental_Information_Disclosure ~ central_connection +
                               ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                               as.factor(industry_type13) + (1 | PROVINCE/CITY),
                               data = dta1_13class)

p4mix2_fixed_central_11 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                               ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                               as.factor(industry_type13) + (1 | PROVINCE/CITY),
                               data = dta1_13class)

p4mix1_fixed_local_11 <- lmer(Environmental_Information_Disclosure ~ local_connection +
                             ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                             as.factor(industry_type13) + (1 | PROVINCE/CITY),
                             data = dta1_13class)
p4mix2_fixed_local_11 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                             ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                             as.factor(industry_type13) + (1 | PROVINCE/CITY),
                             data = dta1_13class)

# 输出结果
tab_model(p3mix1_fixed_industry_11, p3mix2_fixed_industry_11, p3mix3_fixed_industry_11,
          title = "Mixed Effects Models with Industry Fixed Effects (11-Class) - P3 Models",
          dv.labels = "Environmental Information Disclosure",
          rm.terms = c("as.factor"),
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)

tab_model(p4mix1_fixed_central_11, p4mix2_fixed_central_11, p4mix1_fixed_local_11, p4mix2_fixed_local_11,
          title = "Mixed Effects Models with Industry Fixed Effects (11-Class) - P4 Models",
          dv.labels = "Environmental Information Disclosure",
          rm.terms = c("as.factor"),
          show.reflvl = TRUE,
          show.icc = TRUE,
          show.r2 = TRUE)
```

# 新增分析：四种随机效应结构比较

## 随机效应结构比较分析

本节比较四种不同的随机效应结构： 1. **结构A**: (1 \| PROVINCE) - 省份随机截距 2. **结构B**: (1 \| PROVINCE/CITY) - 省份/城市随机截距 3. **结构C**: (1 \| PROVINCE) + (0+x\| industry) - 省份随机截距 + 行业随机斜率 4. **结构D**: (1 \| PROVINCE/CITY) + (0+x\| industry) - 省份/城市随机截距 + 行业随机斜率

其中x代表关键解释变量（P3系列为connection_num，P4系列为central_connection或local_connection）

```{r random_effects_structure_comparison}
# ===== 创建四种随机效应结构的模型 =====

# P3系列模型 - connection_num作为随机斜率变量
cat("=== 创建P3系列四种随机效应结构模型 ===\n")

# P3模型1 - 四种结构
p3_1_structA <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                     RegisterCapital_log +   (1 | PROVINCE),
                     data = dta1_13class)

p3_1_structB <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                     RegisterCapital_log +   (1 | PROVINCE/CITY),
                     data = dta1_13class)

p3_1_structC <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                     RegisterCapital_log +   (1 | PROVINCE) + (0 + connection_num | industry_type13),
                     data = dta1_13class)

p3_1_structD <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + ROA + Leverage +
                     RegisterCapital_log +   (1 | PROVINCE/CITY) + (0 + connection_num | industry_type13),
                     data = dta1_13class)

# P3模型2 - 四种结构
p3_2_structA <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage +
                     RegisterCapital_log +   (1 | PROVINCE),
                     data = dta1_13class)

p3_2_structB <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage +
                     RegisterCapital_log +   (1 | PROVINCE/CITY),
                     data = dta1_13class)

p3_2_structC <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage +
                     RegisterCapital_log +   (1 | PROVINCE) + (0 + after_first_inspection | industry_type13),
                     data = dta1_13class)

p3_2_structD <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + ROA + Leverage +
                     RegisterCapital_log +   (1 | PROVINCE/CITY) + (0 + after_first_inspection | industry_type13),
                     data = dta1_13class)

# P3模型3 - 四种结构（交互效应模型）
p3_3_structA <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                     ESG_Rate + ROA + Leverage + RegisterCapital_log +   (1 | PROVINCE),
                     data = dta1_13class)

p3_3_structB <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                     ESG_Rate + ROA + Leverage + RegisterCapital_log +   (1 | PROVINCE/CITY),
                     data = dta1_13class)

p3_3_structC <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                     ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                     (1 | PROVINCE) + (0 + connection_num | industry_type13),
                     data = dta1_13class)

p3_3_structD <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num +
                     ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                     (1 | PROVINCE/CITY) + (0 + connection_num | industry_type13),
                     data = dta1_13class)

cat("P3系列模型创建完成\n")
```

```{r p4_random_effects_structures}
# P4系列模型 - central_connection和local_connection作为随机斜率变量
cat("=== 创建P4系列四种随机效应结构模型 ===\n")

# P4中央政治关联模型1 - 四种结构
p4_central_1_structA <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage +
                             RegisterCapital_log +   (1 | PROVINCE),
                             data = dta1_13class)

p4_central_1_structB <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage +
                             RegisterCapital_log +   (1 | PROVINCE/CITY),
                             data = dta1_13class)

p4_central_1_structC <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage +
                             RegisterCapital_log +   (1 | PROVINCE) + (0 + central_connection | industry_type13),
                             data = dta1_13class)

p4_central_1_structD <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + ROA + Leverage +
                             RegisterCapital_log +   (1 | PROVINCE/CITY) + (0 + central_connection | industry_type13),
                             data = dta1_13class)

# P4中央政治关联模型2 - 四种结构（交互效应）
p4_central_2_structA <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                             ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                             (1 | PROVINCE),
                             data = dta1_13class)

p4_central_2_structB <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                             ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                             (1 | PROVINCE/CITY),
                             data = dta1_13class)

p4_central_2_structC <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                             ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                             (1 | PROVINCE) + (0 + central_connection | industry_type13),
                             data = dta1_13class)

p4_central_2_structD <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection +
                             ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                             (1 | PROVINCE/CITY) + (0 + central_connection | industry_type13),
                             data = dta1_13class)

# P4地方政治关联模型1 - 四种结构
p4_local_1_structA <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage +
                           RegisterCapital_log +   (1 | PROVINCE),
                           data = dta1_13class)

p4_local_1_structB <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage +
                           RegisterCapital_log +   (1 | PROVINCE/CITY),
                           data = dta1_13class)

p4_local_1_structC <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage +
                           RegisterCapital_log +   (1 | PROVINCE) + (0 + local_connection | industry_type13),
                           data = dta1_13class)

p4_local_1_structD <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + ROA + Leverage +
                           RegisterCapital_log +   (1 | PROVINCE/CITY) + (0 + local_connection | industry_type13),
                           data = dta1_13class)

# P4地方政治关联模型2 - 四种结构（交互效应）
p4_local_2_structA <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                           ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                           (1 | PROVINCE),
                           data = dta1_13class)

p4_local_2_structB <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                           ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                           (1 | PROVINCE/CITY),
                           data = dta1_13class)

p4_local_2_structC <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                           ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                           (1 | PROVINCE) + (0 + local_connection | industry_type13),
                           data = dta1_13class)

p4_local_2_structD <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection +
                           ESG_Rate + ROA + Leverage + RegisterCapital_log +  
                           (1 | PROVINCE/CITY) + (0 + local_connection | industry_type13),
                           data = dta1_13class)

cat("P4系列模型创建完成\n")
```

## 计算四种随机效应结构的性能指标

```{r structure_performance_metrics}
# ===== 计算四种随机效应结构的性能指标 =====
library(performance)
library(MuMIn)

# 创建模型列表
structure_models <- list(
  # P3系列
  "P3_1_StructA" = p3_1_structA,
  "P3_1_StructB" = p3_1_structB,
  "P3_1_StructC" = p3_1_structC,
  "P3_1_StructD" = p3_1_structD,
  "P3_2_StructA" = p3_2_structA,
  "P3_2_StructB" = p3_2_structB,
  "P3_2_StructC" = p3_2_structC,
  "P3_2_StructD" = p3_2_structD,
  "P3_3_StructA" = p3_3_structA,
  "P3_3_StructB" = p3_3_structB,
  "P3_3_StructC" = p3_3_structC,
  "P3_3_StructD" = p3_3_structD,

  # P4中央系列
  "P4_Central_1_StructA" = p4_central_1_structA,
  "P4_Central_1_StructB" = p4_central_1_structB,
  "P4_Central_1_StructC" = p4_central_1_structC,
  "P4_Central_1_StructD" = p4_central_1_structD,
  "P4_Central_2_StructA" = p4_central_2_structA,
  "P4_Central_2_StructB" = p4_central_2_structB,
  "P4_Central_2_StructC" = p4_central_2_structC,
  "P4_Central_2_StructD" = p4_central_2_structD,

  # P4地方系列
  "P4_Local_1_StructA" = p4_local_1_structA,
  "P4_Local_1_StructB" = p4_local_1_structB,
  "P4_Local_1_StructC" = p4_local_1_structC,
  "P4_Local_1_StructD" = p4_local_1_structD,
  "P4_Local_2_StructA" = p4_local_2_structA,
  "P4_Local_2_StructB" = p4_local_2_structB,
  "P4_Local_2_StructC" = p4_local_2_structC,
  "P4_Local_2_StructD" = p4_local_2_structD
)

# 计算性能指标的函数
calculate_structure_metrics <- function(model_list) {
  results <- data.frame()

  for(i in 1:length(model_list)) {
    model <- model_list[[i]]
    model_name <- names(model_list)[i]

    # 提取模型信息
    model_parts <- strsplit(model_name, "_")[[1]]
    model_series <- paste(model_parts[1:2], collapse = "_")
    structure_type <- model_parts[length(model_parts)]

    # 基本信息
    aic_val <- AIC(model)
    bic_val <- BIC(model)
    loglik_val <- as.numeric(logLik(model))

    # R-squared
    r2_vals <- r.squaredGLMM(model)
    r2_marginal <- r2_vals[1]
    r2_conditional <- r2_vals[2]

    # ICC
    icc_val <- performance::icc(model)$ICC_adjusted

    # 自由度和观测数
    df_val <- attr(logLik(model), "df")
    nobs_val <- nobs(model)

    # 组合结果
    row_result <- data.frame(
      Model_Name = model_name,
      Model_Series = model_series,
      Structure = structure_type,
      AIC = aic_val,
      BIC = bic_val,
      LogLik = loglik_val,
      R2_marginal = r2_marginal,
      R2_conditional = r2_conditional,
      ICC = icc_val,
      DF = df_val,
      N_obs = nobs_val,
      stringsAsFactors = FALSE
    )

    results <- rbind(results, row_result)
  }

  return(results)
}

# 计算所有模型的指标
structure_metrics <- calculate_structure_metrics(structure_models)

# 显示结果
cat("=== 三种随机效应结构性能指标 ===\n")
print(structure_metrics)

# 按模型系列分组显示
cat("\n=== 按模型系列分组的结果 ===\n")
for(series in unique(structure_metrics$Model_Series)) {
  cat("\n", series, ":\n")
  series_data <- structure_metrics[structure_metrics$Model_Series == series,
                                   c("Structure", "AIC", "BIC", "LogLik", "R2_marginal", "R2_conditional", "ICC")]
  print(series_data)
}
```

## 似然比检验：四种随机效应结构比较

```{r structure_likelihood_ratio_tests}
# ===== 似然比检验：比较四种随机效应结构 =====

# 创建似然比检验函数
perform_structure_lr_test <- function(model1, model2, model1_name, model2_name) {
  tryCatch({
    lr_test <- anova(model1, model2)

    result <- data.frame(
      Model1 = model1_name,
      Model2 = model2_name,
      Chi_square = lr_test$Chisq[2],
      DF_diff = lr_test$Df[2],
      P_value = lr_test$`Pr(>Chisq)`[2],
      Significant = lr_test$`Pr(>Chisq)`[2] < 0.05,
      Better_Model = ifelse(lr_test$`Pr(>Chisq)`[2] < 0.05, model2_name, "No significant difference"),
      stringsAsFactors = FALSE
    )

    return(result)
  }, error = function(e) {
    # 如果模型不能直接比较，返回NA结果
    result <- data.frame(
      Model1 = model1_name,
      Model2 = model2_name,
      Chi_square = NA,
      DF_diff = NA,
      P_value = NA,
      Significant = NA,
      Better_Model = "Cannot compare (non-nested)",
      stringsAsFactors = FALSE
    )
    return(result)
  })
}

# 进行所有的似然比检验
structure_lr_results <- data.frame()

# 四种结构的检验
model_groups <- list(
  list("P3_1", p3_1_structA, p3_1_structB, p3_1_structC, p3_1_structD),
  list("P3_2", p3_2_structA, p3_2_structB, p3_2_structC, p3_2_structD),
  list("P3_3", p3_3_structA, p3_3_structB, p3_3_structC, p3_3_structD),
  list("P4_Central_1", p4_central_1_structA, p4_central_1_structB, p4_central_1_structC, p4_central_1_structD),
  list("P4_Central_2", p4_central_2_structA, p4_central_2_structB, p4_central_2_structC, p4_central_2_structD),
  list("P4_Local_1", p4_local_1_structA, p4_local_1_structB, p4_local_1_structC, p4_local_1_structD),
  list("P4_Local_2", p4_local_2_structA, p4_local_2_structB, p4_local_2_structC, p4_local_2_structD)
)

for(group in model_groups) {
  group_name <- group[[1]]
  structA <- group[[2]]
  structB <- group[[3]]
  structC <- group[[4]]
  structD <- group[[5]]

  # A vs B
  lr_ab <- perform_structure_lr_test(structA, structB,
                                     paste0(group_name, "_StructA"),
                                     paste0(group_name, "_StructB"))
  structure_lr_results <- rbind(structure_lr_results, lr_ab)

  # A vs C
  lr_ac <- perform_structure_lr_test(structA, structC,
                                     paste0(group_name, "_StructA"),
                                     paste0(group_name, "_StructC"))
  structure_lr_results <- rbind(structure_lr_results, lr_ac)

  # A vs D
  lr_ad <- perform_structure_lr_test(structA, structD,
                                     paste0(group_name, "_StructA"),
                                     paste0(group_name, "_StructD"))
  structure_lr_results <- rbind(structure_lr_results, lr_ad)

  # B vs C
  lr_bc <- perform_structure_lr_test(structB, structC,
                                     paste0(group_name, "_StructB"),
                                     paste0(group_name, "_StructC"))
  structure_lr_results <- rbind(structure_lr_results, lr_bc)

  # B vs D
  lr_bd <- perform_structure_lr_test(structB, structD,
                                     paste0(group_name, "_StructB"),
                                     paste0(group_name, "_StructD"))
  structure_lr_results <- rbind(structure_lr_results, lr_bd)

  # C vs D
  lr_cd <- perform_structure_lr_test(structC, structD,
                                     paste0(group_name, "_StructC"),
                                     paste0(group_name, "_StructD"))
  structure_lr_results <- rbind(structure_lr_results, lr_cd)
}

# 显示似然比检验结果
cat("=== 随机效应结构似然比检验结果 ===\n")
print(structure_lr_results)

# 统计显著性结果
valid_tests <- structure_lr_results[!is.na(structure_lr_results$P_value), ]
significant_improvements <- sum(valid_tests$Significant, na.rm = TRUE)
total_valid_comparisons <- nrow(valid_tests)

cat("\n=== 似然比检验总结 ===\n")
cat("有效比较次数:", total_valid_comparisons, "\n")
cat("显著差异的比较数:", significant_improvements, "\n")
cat("显著差异比例:", round(significant_improvements/total_valid_comparisons*100, 2), "%\n")
```

## 创建四种随机效应结构比较表格

```{r structure_comparison_tables}
# ===== 创建详细的随机效应结构比较表格 =====
library(dplyr)
library(kableExtra)

# 为每个模型组创建比较表
create_structure_comparison_table <- function(series_name) {
  series_data <- structure_metrics[structure_metrics$Model_Series == series_name, ]

  # 计算相对于StructA的变化
  structA_row <- series_data[series_data$Structure == "StructA", ]

  series_data$AIC_diff <- series_data$AIC - structA_row$AIC
  series_data$BIC_diff <- series_data$BIC - structA_row$BIC
  series_data$LogLik_diff <- series_data$LogLik - structA_row$LogLik
  series_data$R2_marginal_diff <- series_data$R2_marginal - structA_row$R2_marginal
  series_data$R2_conditional_diff <- series_data$R2_conditional - structA_row$R2_conditional
  series_data$ICC_diff <- series_data$ICC - structA_row$ICC

  return(series_data)
}

# 创建所有模型系列的比较表
cat("=== 四种随机效应结构详细比较 ===\n")

model_series_list <- unique(structure_metrics$Model_Series)

for(series in model_series_list) {
  cat("\n=== ", series, " 模型比较 ===\n")

  comparison_table <- create_structure_comparison_table(series)

  # 显示主要指标
  display_table <- comparison_table[, c("Structure", "AIC", "BIC", "LogLik",
                                        "R2_marginal", "R2_conditional", "ICC",
                                        "AIC_diff", "LogLik_diff", "ICC_diff")]

  print(display_table)

  # 找出最佳模型（最低AIC）
  best_aic_idx <- which.min(comparison_table$AIC)
  best_model <- comparison_table$Structure[best_aic_idx]
  cat("最佳AIC模型:", best_model, "(AIC =", round(comparison_table$AIC[best_aic_idx], 2), ")\n")

  # 找出最高R²模型
  best_r2_idx <- which.max(comparison_table$R2_conditional)
  best_r2_model <- comparison_table$Structure[best_r2_idx]
  cat("最高条件R²模型:", best_r2_model, "(R² =", round(comparison_table$R2_conditional[best_r2_idx], 4), ")\n")
}

# 创建汇总统计
cat("\n=== 四种随机效应结构汇总统计 ===\n")

structure_summary <- structure_metrics %>%
  group_by(Structure) %>%
  summarise(
    Count = n(),
    Mean_AIC = mean(AIC),
    Mean_BIC = mean(BIC),
    Mean_LogLik = mean(LogLik),
    Mean_R2_marginal = mean(R2_marginal),
    Mean_R2_conditional = mean(R2_conditional),
    Mean_ICC = mean(ICC, na.rm = TRUE),
    .groups = 'drop'
  )

print(structure_summary)

# 统计每种结构在各指标上的表现
cat("\n=== 各结构最佳表现统计 ===\n")

best_aic_count <- structure_metrics %>%
  group_by(Model_Series) %>%
  summarise(Best_AIC = Structure[which.min(AIC)], .groups = 'drop') %>%
  count(Best_AIC)

best_r2_count <- structure_metrics %>%
  group_by(Model_Series) %>%
  summarise(Best_R2 = Structure[which.max(R2_conditional)], .groups = 'drop') %>%
  count(Best_R2)

cat("最佳AIC表现次数:\n")
print(best_aic_count)

cat("\n最佳条件R²表现次数:\n")
print(best_r2_count)
```

## 可视化四种随机效应结构比较

```{r structure_visualization}
# ===== 可视化四种随机效应结构比较 =====
library(ggplot2)
library(gridExtra)
library(tidyr)

# 1. AIC比较图
aic_comparison_plot <- structure_metrics %>%
  ggplot(aes(x = Model_Series, y = AIC, fill = Structure)) +
  geom_bar(stat = "identity", position = "dodge") +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
  labs(title = "四种随机效应结构AIC比较",
       subtitle = "较低的AIC值表示更好的模型拟合",
       x = "模型系列", y = "AIC值") +
  scale_fill_manual(values = c("StructA" = "#E74C3C",
                              "StructB" = "#3498DB",
                              "StructC" = "#2ECC71",
                              "StructD" = "#F39C12"),
                   labels = c("StructA" = "省份随机截距",
                             "StructB" = "省份/城市随机截距",
                             "StructC" = "省份截距+行业斜率",
                             "StructD" = "省份/城市截距+行业斜率"))

print(aic_comparison_plot)

# 2. R²比较图
r2_comparison_plot <- structure_metrics %>%
  select(Model_Series, Structure, R2_marginal, R2_conditional) %>%
  pivot_longer(cols = c(R2_marginal, R2_conditional),
               names_to = "R2_Type", values_to = "Value") %>%
  ggplot(aes(x = Model_Series, y = Value, fill = Structure)) +
  geom_bar(stat = "identity", position = "dodge") +
  facet_wrap(~R2_Type, scales = "free_y",
             labeller = labeller(R2_Type = c("R2_marginal" = "边际R²",
                                            "R2_conditional" = "条件R²"))) +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
  labs(title = "四种随机效应结构R²比较",
       subtitle = "较高的R²值表示更好的解释能力",
       x = "模型系列", y = "R²值") +
  scale_fill_manual(values = c("StructA" = "#E74C3C",
                              "StructB" = "#3498DB",
                              "StructC" = "#2ECC71",
                              "StructD" = "#F39C12"),
                   labels = c("StructA" = "省份随机截距",
                             "StructB" = "省份/城市随机截距",
                             "StructC" = "省份截距+行业斜率",
                             "StructD" = "省份/城市截距+行业斜率"))

print(r2_comparison_plot)

# 3. ICC比较图
icc_comparison_plot <- structure_metrics %>%
  filter(!is.na(ICC)) %>%
  ggplot(aes(x = Model_Series, y = ICC, fill = Structure)) +
  geom_bar(stat = "identity", position = "dodge") +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
  labs(title = "四种随机效应结构ICC比较",
       subtitle = "ICC显示随机效应的重要性",
       x = "模型系列", y = "ICC值") +
  scale_fill_manual(values = c("StructA" = "#E74C3C",
                              "StructB" = "#3498DB",
                              "StructC" = "#2ECC71",
                              "StructD" = "#F39C12"),
                   labels = c("StructA" = "省份随机截距",
                             "StructB" = "省份/城市随机截距",
                             "StructC" = "省份截距+行业斜率",
                             "StructD" = "省份/城市截距+行业斜率"))

print(icc_comparison_plot)

# 4. 性能改善热图
performance_heatmap_data <- structure_metrics %>%
  group_by(Model_Series) %>%
  mutate(
    AIC_rank = rank(AIC),
    R2_rank = rank(-R2_conditional),
    ICC_rank = rank(-ICC, na.last = "keep")
  ) %>%
  select(Model_Series, Structure, AIC_rank, R2_rank, ICC_rank) %>%
  pivot_longer(cols = c(AIC_rank, R2_rank, ICC_rank),
               names_to = "Metric", values_to = "Rank")

performance_heatmap <- performance_heatmap_data %>%
  ggplot(aes(x = Structure, y = Model_Series, fill = Rank)) +
  geom_tile() +
  facet_wrap(~Metric,
             labeller = labeller(Metric = c("AIC_rank" = "AIC排名",
                                           "R2_rank" = "R²排名",
                                           "ICC_rank" = "ICC排名"))) +
  scale_fill_gradient(low = "#2ECC71", high = "#E74C3C",
                     name = "排名\n(1=最佳)") +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
  labs(title = "四种随机效应结构性能排名热图",
       subtitle = "绿色表示更好的排名",
       x = "随机效应结构", y = "模型系列")

print(performance_heatmap)
```

## 四种随机效应结构分析结论

```{r structure_analysis_conclusions}
# ===== 四种随机效应结构分析结论 =====

cat("=== 四种随机效应结构比较分析结论 ===\n\n")

# 1. 整体性能比较
total_models <- nrow(structure_metrics)
total_series <- length(unique(structure_metrics$Model_Series))

cat("1. 分析概述:\n")
cat("   - 比较了四种随机效应结构在", total_series, "个模型系列中的表现\n")
cat("   - 总共分析了", total_models, "个模型\n")
cat("   - 结构A: (1 | PROVINCE) - 省份随机截距\n")
cat("   - 结构B: (1 | PROVINCE/CITY) - 省份/城市随机截距\n")
cat("   - 结构C: (1 | PROVINCE) + (0+x| industry) - 省份随机截距 + 行业随机斜率\n")
cat("   - 结构D: (1 | PROVINCE/CITY) + (0+x| industry) - 省份/城市随机截距 + 行业随机斜率\n\n")

# 2. AIC性能分析
aic_winners <- structure_metrics %>%
  group_by(Model_Series) %>%
  summarise(Best_Structure = Structure[which.min(AIC)], .groups = 'drop') %>%
  count(Best_Structure)

cat("2. AIC信息准则分析:\n")
for(i in 1:nrow(aic_winners)) {
  structure_name <- aic_winners$Best_Structure[i]
  count <- aic_winners$n[i]
  percentage <- round(count/total_series*100, 1)
  cat("   -", structure_name, "获得最佳AIC:", count, "次 (", percentage, "%)\n")
}

# 找出AIC表现最好的结构
best_aic_structure <- aic_winners$Best_Structure[which.max(aic_winners$n)]
cat("   - 最佳AIC结构:", best_aic_structure, "\n\n")

# 3. R²性能分析
r2_winners <- structure_metrics %>%
  group_by(Model_Series) %>%
  summarise(Best_Structure = Structure[which.max(R2_conditional)], .groups = 'drop') %>%
  count(Best_Structure)

cat("3. 条件R²分析:\n")
for(i in 1:nrow(r2_winners)) {
  structure_name <- r2_winners$Best_Structure[i]
  count <- r2_winners$n[i]
  percentage <- round(count/total_series*100, 1)
  cat("   -", structure_name, "获得最高R²:", count, "次 (", percentage, "%)\n")
}

# 找出R²表现最好的结构
best_r2_structure <- r2_winners$Best_Structure[which.max(r2_winners$n)]
cat("   - 最佳R²结构:", best_r2_structure, "\n\n")

# 4. ICC分析
icc_data <- structure_metrics %>%
  filter(!is.na(ICC)) %>%
  group_by(Structure) %>%
  summarise(
    Mean_ICC = mean(ICC),
    Median_ICC = median(ICC),
    Count = n(),
    .groups = 'drop'
  )

cat("4. ICC (组内相关系数) 分析:\n")
for(i in 1:nrow(icc_data)) {
  structure_name <- icc_data$Structure[i]
  mean_icc <- round(icc_data$Mean_ICC[i], 4)
  median_icc <- round(icc_data$Median_ICC[i], 4)
  cat("   -", structure_name, ": 平均ICC =", mean_icc, ", 中位数ICC =", median_icc, "\n")
}

# 5. 似然比检验分析
if(nrow(valid_tests) > 0) {
  cat("\n5. 似然比检验分析:\n")
  cat("   - 有效比较次数:", total_valid_comparisons, "\n")
  cat("   - 显著差异的比较数:", significant_improvements, "\n")
  cat("   - 显著差异比例:", round(significant_improvements/total_valid_comparisons*100, 2), "%\n")

  # 分析哪些比较显示显著差异
  significant_comparisons <- valid_tests[valid_tests$Significant == TRUE, ]
  if(nrow(significant_comparisons) > 0) {
    cat("   - 显著改善的模型比较:\n")
    for(i in 1:nrow(significant_comparisons)) {
      cat("     *", significant_comparisons$Better_Model[i], "显著优于",
          significant_comparisons$Model1[i], "(p <",
          round(significant_comparisons$P_value[i], 4), ")\n")
    }
  }
}

# 6. 综合性能评估
cat("\n6. 综合性能评估:\n")

# 计算每种结构的综合得分
structure_scores <- structure_metrics %>%
  group_by(Structure) %>%
  summarise(
    AIC_wins = sum(Structure %in% (structure_metrics %>%
                                   group_by(Model_Series) %>%
                                   summarise(Best = Structure[which.min(AIC)], .groups = 'drop'))$Best),
    R2_wins = sum(Structure %in% (structure_metrics %>%
                                  group_by(Model_Series) %>%
                                  summarise(Best = Structure[which.max(R2_conditional)], .groups = 'drop'))$Best),
    Mean_AIC_rank = mean(rank(AIC)),
    Mean_R2_rank = mean(rank(-R2_conditional)),
    .groups = 'drop'
  ) %>%
  mutate(
    Total_wins = AIC_wins + R2_wins,
    Overall_rank = rank(Mean_AIC_rank + Mean_R2_rank)
  )

cat("   综合得分排名:\n")
structure_scores_ordered <- structure_scores[order(structure_scores$Overall_rank), ]
for(i in 1:nrow(structure_scores_ordered)) {
  structure_name <- structure_scores_ordered$Structure[i]
  total_wins <- structure_scores_ordered$Total_wins[i]
  aic_wins <- structure_scores_ordered$AIC_wins[i]
  r2_wins <- structure_scores_ordered$R2_wins[i]
  cat("   ", i, ".", structure_name, ": 总获胜", total_wins, "次 (AIC:", aic_wins, "次, R²:", r2_wins, "次)\n")
}

# 7. 最终结论和建议
cat("\n7. 最终结论和建议:\n")

best_overall_structure <- structure_scores_ordered$Structure[1]
cat("   - 综合表现最佳的随机效应结构:", best_overall_structure, "\n")

if(best_overall_structure == "StructA") {
  cat("   - 建议使用省份随机截距结构 (1 | PROVINCE)\n")
  cat("   - 这表明省份层面的随机效应是最重要的\n")
} else if(best_overall_structure == "StructB") {
  cat("   - 建议使用省份/城市随机截距结构 (1 | PROVINCE/CITY)\n")
  cat("   - 这表明需要考虑更细粒度的地理位置随机效应\n")
} else if(best_overall_structure == "StructC") {
  cat("   - 建议使用省份截距+行业随机斜率结构 (1 | PROVINCE) + (0+x| industry)\n")
  cat("   - 这表明需要同时考虑地理和行业的随机效应\n")
} else if(best_overall_structure == "StructD") {
  cat("   - 建议使用省份/城市截距+行业随机斜率结构 (1 | PROVINCE/CITY) + (0+x| industry)\n")
  cat("   - 这表明需要考虑最全面的随机效应结构\n")
}

# 计算一致性
aic_r2_agreement <- sum(aic_winners$Best_Structure == r2_winners$Best_Structure)
cat("   - AIC和R²标准的一致性:", ifelse(aic_r2_agreement > 0, "高", "低"), "\n")

if(aic_r2_agreement == 0) {
  cat("   - 注意: AIC和R²标准给出了不同的最佳结构建议，需要根据研究目标选择\n")
  cat("   - 如果关注模型拟合质量，选择", best_aic_structure, "\n")
  cat("   - 如果关注解释能力，选择", best_r2_structure, "\n")
}

cat("\n=== 分析完成 ===\n")
```